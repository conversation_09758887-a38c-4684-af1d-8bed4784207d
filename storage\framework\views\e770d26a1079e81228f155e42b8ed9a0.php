<ul <?php echo $options; ?>>
    <?php $__currentLoopData = $menu_nodes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $node): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php ($hasMegaMenu = $node->has_child && count($node->child) > 12); ?>
        <li class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                $node->css_class => $node->css_class,
                'has-dropdown' => $node->has_child,
                'has-megamenu' => $hasMegaMenu
            ]); ?>">
            <a href="<?php echo e(url($node->url)); ?>" <?php if($node->target !== '_self'): ?> target="<?php echo e($node->target); ?>" <?php endif; ?> class="<?php echo \Illuminate\Support\Arr::toCssClasses(['mega-menu-title' => $hasMegaMenu]); ?>">
                <?php echo $node->icon_html; ?>


                <?php echo e($node->title); ?>

            </a>

            <?php if($node->has_child): ?>
                <?php if($hasMegaMenu): ?>
                    <?php echo Menu::generateMenu([
                       'menu' => $node,
                       'menu_nodes' => $node->child,
                       'view' => 'mega-menu',
                       'options' => ['class' => 'submenu mega-menu'],
                   ]); ?>

                <?php else: ?>
                    <?php echo Menu::generateMenu([
                        'menu' => $node,
                        'menu_nodes' => $node->child,
                        'view' => 'menu',
                        'options' => ['class' => 'submenu'],
                    ]); ?>

                <?php endif; ?>
            <?php endif; ?>
        </li>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</ul>
<?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/partials/menu.blade.php ENDPATH**/ ?>