<?php if(is_plugin_active('language')): ?>
    <?php
        $locales = Language::getSupportedLocales();
        $languageDisplay = setting('language_display', 'all');
        $showFlag = $languageDisplay === 'all' || $languageDisplay === 'flag';
        $showName = $languageDisplay === 'all' || $languageDisplay === 'name';
        $mobile ??= false;
    ?>

    <?php if($locales && count($locales) > 1): ?>
        <?php if($mobile): ?>
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <span class="me-2">
                        <?php echo language_flag(Language::getCurrentLocaleFlag(), Language::getCurrentLocaleName(), 20); ?>

                    </span>
                    <?php echo e(Language::getCurrentLocaleName()); ?>

                </a>
                <ul class="dropdown-menu">
                    <?php $__currentLoopData = $locales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $locale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($key === Language::getCurrentLocale()) continue; ?>

                        <li>
                            <a class="dropdown-item" href="<?php echo e(Language::getLocalizedURL($key)); ?>">
                                <?php if($showFlag): ?>
                                    <?php echo language_flag($locale['lang_flag'], $locale['lang_name']); ?>

                                <?php endif; ?>
                                <?php if($showName): ?>
                                    <?php echo e($locale['lang_name']); ?>

                                <?php endif; ?>
                            </a>
                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </li>
        <?php else: ?>
            <div class="headertoplag__lang">
                <ul>
                    <li>
                        <button>
                            <?php echo language_flag(Language::getCurrentLocaleFlag(), Language::getCurrentLocaleName()); ?>

                            <?php echo e(Language::getCurrentLocaleName()); ?>

                            <span><i class="fal fa-angle-down"></i></span>
                        </button>
                        <ul class="header-meta__lang-submenu">
                            <?php $__currentLoopData = $locales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $locale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($key === Language::getCurrentLocale()) continue; ?>

                                <li>
                                    <a href="<?php echo e(Language::getLocalizedURL($key)); ?>">
                                        <?php if($showFlag): ?>
                                            <?php echo language_flag($locale['lang_flag'], $locale['lang_name']); ?>

                                        <?php endif; ?>
                                        <?php if($showName): ?>
                                            <?php echo e($locale['lang_name']); ?>

                                        <?php endif; ?>
                                    </a>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </li>
                </ul>
            </div>
        <?php endif; ?>
    <?php endif; ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/partials/language-switcher.blade.php ENDPATH**/ ?>