<div class="navigation-bar">
    <ul class="navigation-bar__list">
        <li>
            <a href="<?php echo e(BaseHelper::getHomepageUrl()); ?>" class="text-truncate" title="<?php echo e(__('Home')); ?>">
                <i class="fal fa-home"></i>
                <?php if($showMenuLabel = theme_option('bottom_mobile_menu_show_label', 'yes') === 'yes'): ?>
                    <span><?php echo e(__('Home')); ?></span>
                <?php endif; ?>
            </a>
        </li>
        <li>
            <a role="button" href="javascript:void(0)" class="tp-categories-sidebar-toggle text-truncate" title="<?php echo e(__('Categories')); ?>">
                <i class="fal fa-list"></i>
                <?php if($showMenuLabel): ?>
                    <span><?php echo e(__('Categories')); ?></span>
                <?php endif; ?>
            </a>
        </li>
        <?php if(EcommerceHelper::isCartEnabled()): ?>
            <li>
                <a role="button" href="javascript:void(0)" class="tp-cart-toggle text-truncate" title="<?php echo e(__('Cart')); ?>">
                    <div class="header-cart p-relative">
                        <i class="fal fa-shopping-cart"></i>
                        <span class="tp-product-count text-white"><?php echo e(Cart::instance('cart')->count()); ?></span>
                    </div>
                    <?php if($showMenuLabel): ?>
                        <span><?php echo e(__('Cart')); ?></span>
                    <?php endif; ?>
                </a>
            </li>
        <?php endif; ?>

        <?php if(EcommerceHelper::isWishlistEnabled()): ?>
            <li>
                <a href="<?php echo e(route('public.wishlist')); ?>" class="text-truncate" title="<?php echo e(__('Wishlist')); ?>">
                    <div class="header-cart p-relative">
                        <i class="fal fa-heart"></i>
                        <span class="tp-product-count tp-product-wishlist-count text-white" style="inset-inline-end: 2px"><?php echo e(Cart::instance('wishlist')->count()); ?></span>
                    </div>
                    <?php if($showMenuLabel): ?>
                        <span><?php echo e(__('Wishlist')); ?></span>
                    <?php endif; ?>
                </a>
            </li>
            <li>
                <a href="<?php echo e(route('customer.overview')); ?>" class="text-truncate" title="<?php echo e(__('Account')); ?>">
                    <i class="fal fa-user"></i>
                    <?php if($showMenuLabel): ?>
                        <span><?php echo e(__('Account')); ?></span>
                    <?php endif; ?>
                </a>
            </li>
        <?php endif; ?>
    </ul>
</div>
<?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/partials/navigation-bar.blade.php ENDPATH**/ ?>