<div
    class="js-cookie-consent cookie-consent cookie-consent-<?php echo e(theme_option('cookie_consent_style', 'full-width')); ?>"
    style="background-color: <?php echo e(theme_option('cookie_consent_background_color', '#000')); ?>; color: <?php echo e(theme_option('cookie_consent_text_color', '#fff')); ?>;"
    dir="<?php echo e(BaseHelper::siteLanguageDirection()); ?>"
>
    <div
        class="cookie-consent-body"
        style="max-width: <?php echo e(theme_option('cookie_consent_max_width', 1170)); ?>px;"
    >
        <div class="cookie-consent__inner">
            <div class="cookie-consent__message">
                <?php echo BaseHelper::clean(theme_option('cookie_consent_message', trans('plugins/cookie-consent::cookie-consent.message'))); ?>

                <?php if(($learnMoreUrl = theme_option('cookie_consent_learn_more_url')) && ($learnMoreText = theme_option('cookie_consent_learn_more_text'))): ?>
                    <a href="<?php echo e(Str::startsWith($learnMoreUrl, ['http://', 'https://']) ? $learnMoreUrl : BaseHelper::getHomepageUrl() . '/' . $learnMoreUrl); ?>"><?php echo e($learnMoreText); ?></a>
                <?php endif; ?>
            </div>

            <div class="cookie-consent__actions">
                <?php if(theme_option('cookie_consent_show_reject_button', 'no') == 'yes'): ?>
                    <button
                        class="js-cookie-consent-reject cookie-consent__reject"
                        style="background-color: <?php echo e(theme_option('cookie_consent_text_color', '#fff')); ?>; color: <?php echo e(theme_option('cookie_consent_background_color', '#000')); ?>; border: 1px solid <?php echo e(theme_option('cookie_consent_text_color', '#fff')); ?>;"
                    >
                        <?php echo e(trans('plugins/cookie-consent::cookie-consent.reject_text')); ?>

                    </button>
                <?php endif; ?>
                <?php if(! empty($cookieConsentConfig['cookie_categories']) && theme_option('cookie_consent_show_customize_button', 'no') == 'yes'): ?>
                    <button
                        class="js-cookie-consent-customize cookie-consent__customize"
                        style="background-color: <?php echo e(theme_option('cookie_consent_text_color', '#fff')); ?>; color: <?php echo e(theme_option('cookie_consent_background_color', '#000')); ?>; border: 1px solid <?php echo e(theme_option('cookie_consent_text_color', '#fff')); ?>;"
                    >
                        <?php echo e(trans('plugins/cookie-consent::cookie-consent.customize_text')); ?>

                    </button>
                <?php endif; ?>
                <button
                    class="js-cookie-consent-agree cookie-consent__agree"
                    style="background-color: <?php echo e(theme_option('cookie_consent_background_color', '#000')); ?>; color: <?php echo e(theme_option('cookie_consent_text_color', '#fff')); ?>; border: 1px solid <?php echo e(theme_option('cookie_consent_text_color', '#fff')); ?>;"
                >
                    <?php echo e(theme_option('cookie_consent_button_text', trans('plugins/cookie-consent::cookie-consent.button_text'))); ?>

                </button>
            </div>
        </div>

        <?php if(! empty($cookieConsentConfig['cookie_categories'])): ?>
            <div class="cookie-consent__categories">
                <?php $__currentLoopData = $cookieConsentConfig['cookie_categories']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="cookie-category">
                        <label class="cookie-category__label">
                            <input type="checkbox"
                                name="cookie_category[]"
                                value="<?php echo e($key); ?>"
                                class="js-cookie-category"
                                <?php if($category['required']): ?> checked disabled <?php endif; ?>
                            >
                            <span class="cookie-category__name"><?php echo e(trans('plugins/cookie-consent::cookie-consent.cookie_categories.' . $key . '.name')); ?></span>
                        </label>
                        <p class="cookie-category__description"><?php echo e(trans('plugins/cookie-consent::cookie-consent.cookie_categories.' . $key . '.description')); ?></p>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                <div class="cookie-consent__save">
                    <button
                        class="js-cookie-consent-save cookie-consent__save-button"
                        style="background-color: <?php echo e(theme_option('cookie_consent_background_color', '#000')); ?>; color: <?php echo e(theme_option('cookie_consent_text_color', '#fff')); ?>; border: 1px solid <?php echo e(theme_option('cookie_consent_text_color', '#fff')); ?>;"
                    >
                        <?php echo e(trans('plugins/cookie-consent::cookie-consent.save_text')); ?>

                    </button>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<div data-site-cookie-name="<?php echo e($cookieConsentConfig['cookie_name'] ?? 'cookie_for_consent'); ?>"></div>
<div data-site-cookie-lifetime="<?php echo e($cookieConsentConfig['cookie_lifetime'] ?? 36000); ?>"></div>
<div data-site-cookie-domain="<?php echo e(config('session.domain') ?? request()->getHost()); ?>"></div>
<div data-site-session-secure="<?php echo e(config('session.secure') ? ';secure' : null); ?>"></div>

<script>
    window.addEventListener('load', function () {
        if (typeof gtag !== 'undefined') {
            gtag('consent', 'default', {
                'ad_storage': 'denied',
                'analytics_storage': 'denied'
            });

            document.addEventListener('click', function(event) {
                if (event.target.classList.contains('js-cookie-consent-agree')) {
                    const categories = document.querySelectorAll('.js-cookie-category:checked');
                    const consents = {
                        'ad_storage': 'denied',
                        'analytics_storage': 'denied'
                    };

                    categories.forEach(function(category) {
                        if (category.value === 'marketing') {
                            consents.ad_storage = 'granted';
                        }
                        if (category.value === 'analytics') {
                            consents.analytics_storage = 'granted';
                        }
                    });

                    gtag('consent', 'update', consents);
                }
            });
        }
    });
</script>
<?php /**PATH C:\xampp\htdocs\main\platform/plugins/cookie-consent/resources/views/index.blade.php ENDPATH**/ ?>