<?php if(is_plugin_active('ecommerce')): ?>
    <div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['header-meta d-flex align-items-center', $class ?? null]); ?>">
        <div class="header-meta__social d-flex align-items-center ml-25">
            <?php if(EcommerceHelper::isCartEnabled()): ?>
                <button class="header-cart p-relative tp-cart-toggle" title="cart">
                    <i class="fal fa-shopping-cart"></i>
                    <span class="tp-product-count"><?php echo e(Cart::instance('cart')->count()); ?></span>
                </button>
            <?php endif; ?>
            <?php if(EcommerceHelper::isCompareEnabled()): ?>
                <a href="#" data-url="<?php echo e(route('public.compare')); ?>" class="header-cart p-relative">
                    <i class="fal fa-exchange"></i>
                    <span class="tp-product-compare-count"><?php echo e(Cart::instance('compare')->count()); ?></span>
                </a>
            <?php endif; ?>
            <?php if(EcommerceHelper::isWishlistEnabled()): ?>
                <a href="<?php echo e(route('public.wishlist')); ?>" class="header-cart p-relative">
                    <i class="fal fa-heart"></i>
                    <span class="tp-product-wishlist-count"><?php echo e(Cart::instance('wishlist')->count()); ?></span>
                </a>
            <?php endif; ?>
            <?php if(auth()->guard('customer')->check()): ?>
                <a href="<?php echo e(route('customer.overview')); ?>" title="<?php echo e(auth('customer')->user()->name); ?>"><i class="fal fa-user"></i></a>
            <?php else: ?>
                <a href="<?php echo e(route('customer.login')); ?>" title="<?php echo e(__('Login')); ?>"><i class="fal fa-user"></i></a>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/partials/header-meta.blade.php ENDPATH**/ ?>