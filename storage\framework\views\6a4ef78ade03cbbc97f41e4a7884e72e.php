<?php if(is_plugin_active('ecommerce') && EcommerceHelper::isCartEnabled()): ?>
    <div class="tpcart__product-list">
        <ul>
            <?php ($products = Cart::instance('cart')->products()); ?>
            <?php $__empty_1 = true; $__currentLoopData = Cart::instance('cart')->content(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $cartItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <?php ($product = $products->find($cartItem->id)); ?>

                <?php if(! $product) continue; ?>

                <li>
                    <div class="tpcart__item">
                        <div class="tpcart__img">
                            <img src="<?php echo e(RvMedia::getImageUrl(Arr::get($cartItem->options, 'image'), 'thumb', false, RvMedia::getDefaultImage())); ?>" alt="<?php echo e($product->original_product->name); ?>">

                            <div class="tpcart__del">
                                <a href="#" data-url="<?php echo e(route('public.cart.remove', $cartItem->rowId)); ?>" class="remove-cart-item"><i class="far fa-times-circle"></i></a>
                            </div>
                        </div>
                        <div class="tpcart__content">
                            <span class="tpcart__content-title">
                               <a href="<?php echo e($product->original_product->url); ?>"><?php echo e($product->original_product->name); ?></a>
                            </span>
                            <div class="tpcart__cart-price">
                                <span class="quantity"><?php echo e(__(':qty x', ['qty' => $cartItem->qty])); ?></span>
                                <span class="new-price">
                                    <bdi>
                                        <?php echo e(format_price($cartItem->price)); ?> <?php if($product->front_sale_price != $product->price): ?>
                                            <span class="tpproduct__priceinfo-list-oldprice"><?php echo e(format_price($product->price)); ?></span>
                                        <?php endif; ?>
                                    </bdi>
                                </span>

                                <span class="d-inline-block mb-0">
                                    <?php echo e(Arr::get($cartItem->options, 'attributes', '')); ?>

                                </span>
                                <?php if(EcommerceHelper::isEnabledProductOptions() && ! empty($cartItem->options['options'])): ?>
                                    <?php echo render_product_options_html($cartItem->options['options'], $product->original_price); ?>

                                <?php endif; ?>

                                <?php echo $__env->make('plugins/ecommerce::themes.includes.cart-item-options-extras', ['options' => $cartItem->options], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            </div>
                        </div>
                    </div>
                </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <li class="text-center small text-muted"><?php echo e(__('Your cart is empty!')); ?></li>
            <?php endif; ?>
        </ul>
    </div>

    <?php if(Cart::instance('cart')->count() > 0 && Cart::instance('cart')->products()->count() > 0): ?>
        <div class="tpcart__checkout">
            <div class="tpcart__total-price">
                <div class="text-black d-flex justify-content-between align-items-center mb-2">
                    <span><?php echo e(__('Subtotal:')); ?></span>
                    <span><?php echo e(format_price(Cart::instance('cart')->rawSubTotal())); ?></span>
                </div>
                <div class="text-black d-flex justify-content-between align-items-center mb-2">
                    <span><?php echo e(__('Tax:')); ?></span>
                    <span><?php echo e(format_price(Cart::instance('cart')->rawTax())); ?></span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span><?php echo e(__('Total:')); ?></span>
                    <span class="heilight-price"><?php echo e(format_price(Cart::instance('cart')->rawSubTotal() + Cart::instance('cart')->rawTax())); ?></span>
                </div>
            </div>
            <div class="tpcart__checkout-btn">
                <a class="tpcart-btn mb-10" href="<?php echo e(route('public.cart')); ?>"><?php echo e(__('View Cart')); ?></a>
                <a class="tpcheck-btn" href="<?php echo e(route('public.checkout.information', OrderHelper::getOrderSessionToken())); ?>"><?php echo e(__('Checkout')); ?></a>
            </div>
        </div>
    <?php endif; ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/views/ecommerce/includes/mini-cart.blade.php ENDPATH**/ ?>