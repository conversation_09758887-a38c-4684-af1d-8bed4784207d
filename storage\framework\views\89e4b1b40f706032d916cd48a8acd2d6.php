<?php
    SeoHelper::setTitle(__('404 - Not found'));
    Theme::fireEventGlobalAssets();
?>



<?php $__env->startSection('content'); ?>
    <?php echo Theme::partial('headers.default'); ?>


    <main>
        <section class="erroe-area pt-70 pb-70">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="eperror__wrapper text-center">
                            <div class="tperror__thumb mb-35">
                                <img src="<?php echo e(RvMedia::getImageUrl(theme_option('404_not_found_icon'), default: RvMedia::getDefaultImage())); ?>" alt="<?php echo e(theme_option('site_title')); ?>">
                            </div>
                            <div class="tperror__content">
                                <h4 class="tperror__title mb-25"><?php echo e(__('404. Page not found')); ?></h4>
                                <p><?php echo __('Sorry, we couldn’t find the page you where looking for. We suggest that <br> you return to homepage.'); ?></p>
                                <a href="<?php echo e(BaseHelper::getHomepageUrl()); ?>" class="tpsecondary-btn tp-color-btn tp-error-btn">
                                    <i class="fal fa-long-arrow-left"></i> <?php echo e(__('Back To Home')); ?>

                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <?php echo Theme::partial('footer'); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make(Theme::getThemeNamespace('layouts.base'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/views/404.blade.php ENDPATH**/ ?>