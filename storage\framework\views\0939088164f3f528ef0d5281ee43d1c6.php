<!doctype html>
<html <?php echo Theme::htmlAttributes(); ?>>
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=5, user-scalable=1" name="viewport"/>
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        <?php echo BaseHelper::googleFonts(sprintf('https://fonts.googleapis.com/css2?family=%s:wght@400;500;600', urlencode($primaryFont = theme_option('primary_font', 'Jost')))); ?>


        <style>
            :root {
                --primary-color: <?php echo e($primaryColor = theme_option('primary_color', '#d51243')); ?>;
                --primary-color-rgb: <?php echo e(implode(',', BaseHelper::hexToRgb($primaryColor))); ?>;
                --primary-font: '<?php echo e($primaryFont); ?>', sans-serif;
                --header-background-color: <?php echo e(theme_option('header_background_color', '#fff')); ?>;
                --header-text-color: <?php echo e(theme_option('header_text_color', '#040404')); ?>;
                --header-menu-text-color: <?php echo e(theme_option('header_menu_text_color', '#040404')); ?>;
                --header-menu-text-hover-color: <?php echo e(theme_option('header_menu_text_hover_color', $primaryColor)); ?>;
            }
        </style>

        <?php echo Theme::header(); ?>

    </head>
    <body <?php echo Theme::bodyAttributes(); ?>>
        <?php echo apply_filters(THEME_FRONT_BODY, null); ?>


        <?php echo Theme::partial('scroll-top'); ?>


        <?php echo $__env->yieldContent('content'); ?>

        <?php if(is_plugin_active('ecommerce') && theme_option('bottom_mobile_menu_enabled', 'yes') === 'yes'): ?>
            <?php echo $__env->make(Theme::getThemeNamespace('partials.navigation-bar'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php endif; ?>

        <script>
            'use strict';

            window.trans = {};
            window.siteConfig = {};
            <?php if(is_plugin_active('ecommerce')): ?>
                window.currencies = <?php echo json_encode(get_currencies_json(), 15, 512) ?>;
                <?php if(EcommerceHelper::isCartEnabled()): ?>
                    siteConfig.ajaxCart = '<?php echo e(route('public.ajax.cart')); ?>';
                    siteConfig.cartUrl = '<?php echo e(route('public.cart')); ?>';
                <?php endif; ?>
            <?php endif; ?>
        </script>

        <?php echo Theme::footer(); ?>

    </body>
</html>
<?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/layouts/base.blade.php ENDPATH**/ ?>