<ul <?php echo $options; ?>>
    <?php $__currentLoopData = $menu_nodes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $node): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <?php
        $title = $node->title;
    ?>

        <li class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                $node->css_class => $node->css_class,
                'has-dropdown' => $node->has_child
            ]); ?>">
            <a href="<?php echo e(url($node->url)); ?>" <?php if($node->target !== '_self'): ?> target="<?php echo e($node->target); ?>" <?php endif; ?>>
                <?php echo $node->icon_html; ?>


                <?php if($title): ?>
                    <span class="title"><?php echo e($title); ?></span>
                <?php endif; ?>
            </a>

            <?php if($node->has_child): ?>
                <?php echo Menu::generateMenu([
                    'menu' => $node,
                    'menu_nodes' => $node->child,
                    'view' => 'menu',
                    'options' => ['class' => 'submenu'],
                ]); ?>

            <?php endif; ?>
        </li>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</ul>
<?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/partials/mobile-menu.blade.php ENDPATH**/ ?>