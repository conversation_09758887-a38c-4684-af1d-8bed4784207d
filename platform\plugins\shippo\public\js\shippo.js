(()=>{"use strict";var e=e||{};e.init=function(){$(document).on("show.bs.modal","#shippo-view-n-create-transaction",(function(e){var t=$(e.currentTarget),o=$(e.relatedTarget);t.find(".modal-body").html(""),$.ajax({type:"GET",url:o.data("url"),beforeSend:function(){o.addClass("button-loading")},success:function(e){e.error?Botble.showError(e.message):t.find(".modal-body").html(e.data.html)},error:function(e){Botble.handleError(e)},complete:function(){o.removeClass("button-loading")}})})),$(document).on("click","#shippo-view-n-create-transaction .create-transaction",(function(e){var t=$(e.currentTarget);$.ajax({type:"POST",url:t.data("url"),beforeSend:function(){t.addClass("button-loading")},success:function(e){e.error?Botble.showError(e.message):($('[data-bs-target="#shippo-view-n-create-transaction"]').addClass("d-none"),$("#shippo-view-n-create-transaction").modal("hide"),Botble.showSuccess(e.message))},error:function(e){Botble.handleError(e)},complete:function(){t.removeClass("button-loading")}})})),$(document).on("click","#shippo-view-n-create-transaction .get-new-rates",(function(e){var t=$(e.currentTarget);$.ajax({type:"GET",url:t.data("url"),beforeSend:function(){t.addClass("button-loading")},success:function(e){e.error?Botble.showError(e.message):(Botble.showSuccess(e.message),t.addClass("d-none"),t.parent().append(e.data.html))},error:function(e){Botble.handleError(e)},complete:function(){t.removeClass("button-loading")}})})),$(document).on("submit",".update-rate-shipment",(function(e){e.preventDefault();var t=$(e.currentTarget),o=t.find("button[type=submit]");$.ajax({type:"POST",url:t.prop("action"),data:t.serializeArray(),beforeSend:function(){o.addClass("button-loading")},success:function(e){e.error?Botble.showError(e.message):(Botble.showSuccess(e.message),$("#shippo-view-n-create-transaction").find(".modal-body").html(e.data.html))},error:function(e){Botble.handleError(e)},complete:function(){o.removeClass("button-loading")}})}))},$((function(){e.init()}))})();