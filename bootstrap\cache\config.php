<?php return array (
  2 => 'broadcasting',
  4 => 'concurrency',
  5 => 'cors',
  8 => 'hashing',
  14 => 'view',
  'app' => 
  array (
    'name' => 'Your App',
    'env' => 'local',
    'debug' => true,
    'url' => 'http://localhost/main',
    'frontend_url' => 'http://localhost:3000',
    'asset_url' => NULL,
    'timezone' => 'UTC',
    'locale' => 'en',
    'fallback_locale' => 'en',
    'faker_locale' => 'en_US',
    'cipher' => 'AES-256-CBC',
    'key' => 'base64:aupx1RSuyw8STqR4i3nxG5Xks5OGwbZN5xelUG0NVPE=',
    'previous_keys' => 
    array (
    ),
    'maintenance' => 
    array (
      'driver' => 'file',
      'store' => 'database',
    ),
    'providers' => 
    array (
      0 => 'Illuminate\\Auth\\AuthServiceProvider',
      1 => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
      2 => 'Illuminate\\Bus\\BusServiceProvider',
      3 => 'Illuminate\\Cache\\CacheServiceProvider',
      4 => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
      5 => 'Illuminate\\Concurrency\\ConcurrencyServiceProvider',
      6 => 'Illuminate\\Cookie\\CookieServiceProvider',
      7 => 'Illuminate\\Database\\DatabaseServiceProvider',
      8 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
      9 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
      10 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
      11 => 'Illuminate\\Hashing\\HashServiceProvider',
      12 => 'Illuminate\\Mail\\MailServiceProvider',
      13 => 'Illuminate\\Notifications\\NotificationServiceProvider',
      14 => 'Illuminate\\Pagination\\PaginationServiceProvider',
      15 => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
      16 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
      17 => 'Illuminate\\Queue\\QueueServiceProvider',
      18 => 'Illuminate\\Redis\\RedisServiceProvider',
      19 => 'Illuminate\\Session\\SessionServiceProvider',
      20 => 'Illuminate\\Translation\\TranslationServiceProvider',
      21 => 'Illuminate\\Validation\\ValidationServiceProvider',
      22 => 'Illuminate\\View\\ViewServiceProvider',
    ),
    'aliases' => 
    array (
      'App' => 'Illuminate\\Support\\Facades\\App',
      'Arr' => 'Illuminate\\Support\\Arr',
      'Artisan' => 'Illuminate\\Support\\Facades\\Artisan',
      'Auth' => 'Illuminate\\Support\\Facades\\Auth',
      'Blade' => 'Illuminate\\Support\\Facades\\Blade',
      'Broadcast' => 'Illuminate\\Support\\Facades\\Broadcast',
      'Bus' => 'Illuminate\\Support\\Facades\\Bus',
      'Cache' => 'Illuminate\\Support\\Facades\\Cache',
      'Concurrency' => 'Illuminate\\Support\\Facades\\Concurrency',
      'Config' => 'Illuminate\\Support\\Facades\\Config',
      'Context' => 'Illuminate\\Support\\Facades\\Context',
      'Cookie' => 'Illuminate\\Support\\Facades\\Cookie',
      'Crypt' => 'Illuminate\\Support\\Facades\\Crypt',
      'Date' => 'Illuminate\\Support\\Facades\\Date',
      'DB' => 'Illuminate\\Support\\Facades\\DB',
      'Eloquent' => 'Illuminate\\Database\\Eloquent\\Model',
      'Event' => 'Illuminate\\Support\\Facades\\Event',
      'File' => 'Illuminate\\Support\\Facades\\File',
      'Gate' => 'Illuminate\\Support\\Facades\\Gate',
      'Hash' => 'Illuminate\\Support\\Facades\\Hash',
      'Http' => 'Illuminate\\Support\\Facades\\Http',
      'Js' => 'Illuminate\\Support\\Js',
      'Lang' => 'Illuminate\\Support\\Facades\\Lang',
      'Log' => 'Illuminate\\Support\\Facades\\Log',
      'Mail' => 'Illuminate\\Support\\Facades\\Mail',
      'Notification' => 'Illuminate\\Support\\Facades\\Notification',
      'Number' => 'Illuminate\\Support\\Number',
      'Password' => 'Illuminate\\Support\\Facades\\Password',
      'Process' => 'Illuminate\\Support\\Facades\\Process',
      'Queue' => 'Illuminate\\Support\\Facades\\Queue',
      'RateLimiter' => 'Illuminate\\Support\\Facades\\RateLimiter',
      'Redirect' => 'Illuminate\\Support\\Facades\\Redirect',
      'Request' => 'Illuminate\\Support\\Facades\\Request',
      'Response' => 'Illuminate\\Support\\Facades\\Response',
      'Route' => 'Illuminate\\Support\\Facades\\Route',
      'Schedule' => 'Illuminate\\Support\\Facades\\Schedule',
      'Schema' => 'Illuminate\\Support\\Facades\\Schema',
      'Session' => 'Illuminate\\Support\\Facades\\Session',
      'Storage' => 'Illuminate\\Support\\Facades\\Storage',
      'Str' => 'Illuminate\\Support\\Str',
      'URL' => 'Illuminate\\Support\\Facades\\URL',
      'Uri' => 'Illuminate\\Support\\Uri',
      'Validator' => 'Illuminate\\Support\\Facades\\Validator',
      'View' => 'Illuminate\\Support\\Facades\\View',
      'Vite' => 'Illuminate\\Support\\Facades\\Vite',
    ),
    'debug_blacklist' => 
    array (
      '_ENV' => 
      array (
        0 => 'APP_KEY',
        1 => 'ADMIN_DIR',
        2 => 'DB_DATABASE',
        3 => 'DB_USERNAME',
        4 => 'DB_PASSWORD',
        5 => 'REDIS_PASSWORD',
        6 => 'MAIL_PASSWORD',
        7 => 'PUSHER_APP_KEY',
        8 => 'PUSHER_APP_SECRET',
      ),
      '_SERVER' => 
      array (
        0 => 'APP_KEY',
        1 => 'ADMIN_DIR',
        2 => 'DB_DATABASE',
        3 => 'DB_USERNAME',
        4 => 'DB_PASSWORD',
        5 => 'REDIS_PASSWORD',
        6 => 'MAIL_PASSWORD',
        7 => 'PUSHER_APP_KEY',
        8 => 'PUSHER_APP_SECRET',
      ),
      '_POST' => 
      array (
        0 => 'password',
      ),
    ),
  ),
  'auth' => 
  array (
    'defaults' => 
    array (
      'guard' => 'web',
      'passwords' => 'users',
    ),
    'guards' => 
    array (
      'web' => 
      array (
        'driver' => 'session',
        'provider' => 'users',
      ),
      'sanctum' => 
      array (
        'driver' => 'sanctum',
        'provider' => NULL,
      ),
      'customer' => 
      array (
        'driver' => 'session',
        'provider' => 'customers',
      ),
    ),
    'providers' => 
    array (
      'users' => 
      array (
        'driver' => 'eloquent',
        'model' => 'Botble\\ACL\\Models\\User',
      ),
      'customers' => 
      array (
        'driver' => 'eloquent',
        'model' => 'Botble\\Ecommerce\\Models\\Customer',
      ),
    ),
    'passwords' => 
    array (
      'users' => 
      array (
        'provider' => 'users',
        'table' => 'password_reset_tokens',
        'expire' => 60,
        'throttle' => 60,
      ),
      'customers' => 
      array (
        'provider' => 'customers',
        'table' => 'ec_customer_password_resets',
        'expire' => 60,
      ),
    ),
    'password_timeout' => 10800,
  ),
  'cache' => 
  array (
    'default' => 'file',
    'stores' => 
    array (
      'array' => 
      array (
        'driver' => 'array',
        'serialize' => false,
      ),
      'database' => 
      array (
        'driver' => 'database',
        'connection' => NULL,
        'table' => 'cache',
        'lock_connection' => NULL,
        'lock_table' => NULL,
      ),
      'file' => 
      array (
        'driver' => 'file',
        'path' => 'C:\\xampp\\htdocs\\main\\storage\\framework/cache/data',
        'lock_path' => 'C:\\xampp\\htdocs\\main\\storage\\framework/cache/data',
      ),
      'memcached' => 
      array (
        'driver' => 'memcached',
        'persistent_id' => NULL,
        'sasl' => 
        array (
          0 => NULL,
          1 => NULL,
        ),
        'options' => 
        array (
        ),
        'servers' => 
        array (
          0 => 
          array (
            'host' => '127.0.0.1',
            'port' => 11211,
            'weight' => 100,
          ),
        ),
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'cache',
        'lock_connection' => 'default',
      ),
      'dynamodb' => 
      array (
        'driver' => 'dynamodb',
        'key' => NULL,
        'secret' => NULL,
        'region' => 'us-east-1',
        'table' => 'cache',
        'endpoint' => NULL,
      ),
      'octane' => 
      array (
        'driver' => 'octane',
      ),
    ),
    'prefix' => 'your_app_cache_',
  ),
  'database' => 
  array (
    'default' => 'mysql',
    'connections' => 
    array (
      'sqlite' => 
      array (
        'driver' => 'sqlite',
        'url' => NULL,
        'database' => 'ninico',
        'prefix' => '',
        'foreign_key_constraints' => true,
        'busy_timeout' => NULL,
        'journal_mode' => NULL,
        'synchronous' => NULL,
      ),
      'mysql' => 
      array (
        'driver' => 'mysql',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'ninico',
        'username' => 'root',
        'password' => '',
        'unix_socket' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'strict' => false,
        'engine' => NULL,
        'options' => 
        array (
        ),
      ),
      'mariadb' => 
      array (
        'driver' => 'mariadb',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'ninico',
        'username' => 'root',
        'password' => '',
        'unix_socket' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'strict' => true,
        'engine' => NULL,
        'options' => 
        array (
        ),
      ),
      'pgsql' => 
      array (
        'driver' => 'pgsql',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'ninico',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
        'search_path' => 'public',
        'sslmode' => 'prefer',
      ),
      'sqlsrv' => 
      array (
        'driver' => 'sqlsrv',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'ninico',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
      ),
    ),
    'migrations' => 
    array (
      'table' => 'migrations',
      'update_date_on_publish' => true,
    ),
    'redis' => 
    array (
      'client' => 'predis',
      'options' => 
      array (
        'cluster' => 'redis',
        'prefix' => 'your_app_database_',
        'persistent' => false,
      ),
      'default' => 
      array (
        'url' => NULL,
        'host' => '127.0.0.1',
        'username' => NULL,
        'password' => NULL,
        'port' => '6379',
        'database' => '0',
      ),
      'cache' => 
      array (
        'url' => NULL,
        'host' => '127.0.0.1',
        'username' => NULL,
        'password' => NULL,
        'port' => '6379',
        'database' => '1',
      ),
    ),
  ),
  'filesystems' => 
  array (
    'default' => 'public',
    'disks' => 
    array (
      'local' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\main\\storage\\app',
        'serve' => true,
        'throw' => false,
        'report' => false,
      ),
      'public' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\main\\public\\storage',
        'url' => 'http://localhost/main/storage',
        'visibility' => 'public',
        'throw' => true,
        'report' => false,
      ),
      's3' => 
      array (
        'driver' => 's3',
        'key' => NULL,
        'secret' => NULL,
        'region' => NULL,
        'bucket' => NULL,
        'url' => NULL,
        'endpoint' => NULL,
        'use_path_style_endpoint' => false,
        'throw' => false,
        'report' => false,
      ),
      'fcache' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\main\\storage\\framework/cache/data',
      ),
    ),
    'links' => 
    array (
      'C:\\xampp\\htdocs\\main\\public\\storage' => 'C:\\xampp\\htdocs\\main\\storage\\app/public',
    ),
  ),
  'logging' => 
  array (
    'default' => 'daily',
    'deprecations' => 
    array (
      'channel' => 'null',
      'trace' => false,
    ),
    'channels' => 
    array (
      'stack' => 
      array (
        'driver' => 'stack',
        'channels' => 
        array (
          0 => 'single',
        ),
        'ignore_exceptions' => false,
      ),
      'single' => 
      array (
        'driver' => 'single',
        'path' => 'C:\\xampp\\htdocs\\main\\storage\\logs/laravel.log',
        'level' => 'debug',
        'replace_placeholders' => true,
      ),
      'daily' => 
      array (
        'driver' => 'daily',
        'path' => 'C:\\xampp\\htdocs\\main\\storage\\logs/laravel.log',
        'level' => 'debug',
        'days' => 14,
        'replace_placeholders' => true,
      ),
      'slack' => 
      array (
        'driver' => 'slack',
        'url' => NULL,
        'username' => 'Laravel Log',
        'emoji' => ':boom:',
        'level' => 'critical',
        'replace_placeholders' => true,
      ),
      'papertrail' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\SyslogUdpHandler',
        'handler_with' => 
        array (
          'host' => NULL,
          'port' => NULL,
          'connectionString' => 'tls://:',
        ),
        'processors' => 
        array (
          0 => 'Monolog\\Processor\\PsrLogMessageProcessor',
        ),
      ),
      'stderr' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\StreamHandler',
        'formatter' => NULL,
        'with' => 
        array (
          'stream' => 'php://stderr',
        ),
        'processors' => 
        array (
          0 => 'Monolog\\Processor\\PsrLogMessageProcessor',
        ),
      ),
      'syslog' => 
      array (
        'driver' => 'syslog',
        'level' => 'debug',
        'facility' => 8,
        'replace_placeholders' => true,
      ),
      'errorlog' => 
      array (
        'driver' => 'errorlog',
        'level' => 'debug',
        'replace_placeholders' => true,
      ),
      'null' => 
      array (
        'driver' => 'monolog',
        'handler' => 'Monolog\\Handler\\NullHandler',
      ),
      'emergency' => 
      array (
        'path' => 'C:\\xampp\\htdocs\\main\\storage\\logs/laravel.log',
      ),
      'deprecations' => 
      array (
        'driver' => 'single',
        'path' => 'C:\\xampp\\htdocs\\main\\storage\\logs/php-deprecation-warnings.log',
      ),
      'shippo' => 
      array (
        'driver' => 'daily',
        'path' => 'C:\\xampp\\htdocs\\main\\storage\\logs/shippo.log',
      ),
    ),
  ),
  'mail' => 
  array (
    'default' => 'log',
    'mailers' => 
    array (
      'smtp' => 
      array (
        'transport' => 'smtp',
        'scheme' => NULL,
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => 2525,
        'username' => NULL,
        'password' => NULL,
        'timeout' => NULL,
        'local_domain' => 'localhost',
      ),
      'ses' => 
      array (
        'transport' => 'ses',
      ),
      'postmark' => 
      array (
        'transport' => 'postmark',
      ),
      'resend' => 
      array (
        'transport' => 'resend',
      ),
      'sendmail' => 
      array (
        'transport' => 'sendmail',
        'path' => '/usr/sbin/sendmail -bs -i',
      ),
      'log' => 
      array (
        'transport' => 'log',
        'channel' => NULL,
      ),
      'array' => 
      array (
        'transport' => 'array',
      ),
      'failover' => 
      array (
        'transport' => 'failover',
        'mailers' => 
        array (
          0 => 'smtp',
          1 => 'log',
        ),
      ),
      'roundrobin' => 
      array (
        'transport' => 'roundrobin',
        'mailers' => 
        array (
          0 => 'ses',
          1 => 'postmark',
        ),
      ),
      'mailgun' => 
      array (
        'transport' => 'mailgun',
      ),
    ),
    'from' => 
    array (
      'address' => '<EMAIL>',
      'name' => 'Example',
    ),
    'markdown' => 
    array (
      'theme' => 'default',
      'paths' => 
      array (
        0 => 'C:\\xampp\\htdocs\\main\\resources\\views/vendor/mail',
      ),
    ),
  ),
  'queue' => 
  array (
    'default' => 'sync',
    'connections' => 
    array (
      'sync' => 
      array (
        'driver' => 'sync',
      ),
      'database' => 
      array (
        'driver' => 'database',
        'connection' => NULL,
        'table' => 'jobs',
        'queue' => 'default',
        'retry_after' => 90,
        'after_commit' => false,
      ),
      'beanstalkd' => 
      array (
        'driver' => 'beanstalkd',
        'host' => 'localhost',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => 0,
        'after_commit' => false,
      ),
      'sqs' => 
      array (
        'driver' => 'sqs',
        'key' => NULL,
        'secret' => NULL,
        'prefix' => 'https://sqs.us-east-1.amazonaws.com/your-account-id',
        'queue' => 'default',
        'suffix' => NULL,
        'region' => 'us-east-1',
        'after_commit' => false,
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => NULL,
        'after_commit' => false,
      ),
    ),
    'batching' => 
    array (
      'database' => 'mysql',
      'table' => 'job_batches',
    ),
    'failed' => 
    array (
      'driver' => 'database-uuids',
      'database' => 'mysql',
      'table' => 'failed_jobs',
    ),
  ),
  'services' => 
  array (
    'postmark' => 
    array (
      'token' => NULL,
    ),
    'ses' => 
    array (
      'key' => NULL,
      'secret' => NULL,
      'region' => 'us-east-1',
    ),
    'resend' => 
    array (
      'key' => NULL,
    ),
    'slack' => 
    array (
      'notifications' => 
      array (
        'bot_user_oauth_token' => NULL,
        'channel' => NULL,
      ),
    ),
  ),
  'session' => 
  array (
    'driver' => 'file',
    'lifetime' => 3600,
    'expire_on_close' => false,
    'encrypt' => false,
    'files' => 'C:\\xampp\\htdocs\\main\\storage\\framework/sessions',
    'connection' => NULL,
    'table' => 'sessions',
    'store' => NULL,
    'lottery' => 
    array (
      0 => 2,
      1 => 100,
    ),
    'cookie' => 'botble_session',
    'path' => '/',
    'domain' => NULL,
    'secure' => NULL,
    'http_only' => true,
    'same_site' => 'lax',
    'partitioned' => false,
  ),
  'broadcasting' => 
  array (
    'default' => 'null',
    'connections' => 
    array (
      'reverb' => 
      array (
        'driver' => 'reverb',
        'key' => NULL,
        'secret' => NULL,
        'app_id' => NULL,
        'options' => 
        array (
          'host' => NULL,
          'port' => 443,
          'scheme' => 'https',
          'useTLS' => true,
        ),
        'client_options' => 
        array (
        ),
      ),
      'pusher' => 
      array (
        'driver' => 'pusher',
        'key' => NULL,
        'secret' => NULL,
        'app_id' => NULL,
        'options' => 
        array (
          'cluster' => NULL,
          'host' => 'api-mt1.pusher.com',
          'port' => 443,
          'scheme' => 'https',
          'encrypted' => true,
          'useTLS' => true,
        ),
        'client_options' => 
        array (
        ),
      ),
      'ably' => 
      array (
        'driver' => 'ably',
        'key' => NULL,
      ),
      'log' => 
      array (
        'driver' => 'log',
      ),
      'null' => 
      array (
        'driver' => 'null',
      ),
    ),
  ),
  'concurrency' => 
  array (
    'default' => 'process',
  ),
  'cors' => 
  array (
    'paths' => 
    array (
      0 => 'api/*',
      1 => 'sanctum/csrf-cookie',
    ),
    'allowed_methods' => 
    array (
      0 => '*',
    ),
    'allowed_origins' => 
    array (
      0 => '*',
    ),
    'allowed_origins_patterns' => 
    array (
    ),
    'allowed_headers' => 
    array (
      0 => '*',
    ),
    'exposed_headers' => 
    array (
    ),
    'max_age' => 0,
    'supports_credentials' => false,
  ),
  'hashing' => 
  array (
    'driver' => 'bcrypt',
    'bcrypt' => 
    array (
      'rounds' => 12,
      'verify' => true,
      'limit' => NULL,
    ),
    'argon' => 
    array (
      'memory' => 65536,
      'threads' => 1,
      'time' => 4,
      'verify' => true,
    ),
    'rehash_on_login' => true,
  ),
  'view' => 
  array (
    'paths' => 
    array (
      0 => 'C:\\xampp\\htdocs\\main\\resources\\views',
    ),
    'compiled' => 'C:\\xampp\\htdocs\\main\\storage\\framework\\views',
  ),
  'dompdf' => 
  array (
    'show_warnings' => false,
    'public_path' => 'C:\\xampp\\htdocs\\main\\public',
    'convert_entities' => true,
    'options' => 
    array (
      'font_dir' => 'C:\\xampp\\htdocs\\main\\storage\\fonts',
      'font_cache' => 'C:\\xampp\\htdocs\\main\\storage\\fonts',
      'temp_dir' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp',
      'chroot' => 'C:\\xampp\\htdocs\\main',
      'allowed_protocols' => 
      array (
        'data://' => 
        array (
          'rules' => 
          array (
          ),
        ),
        'file://' => 
        array (
          'rules' => 
          array (
          ),
        ),
        'http://' => 
        array (
          'rules' => 
          array (
          ),
        ),
        'https://' => 
        array (
          'rules' => 
          array (
          ),
        ),
      ),
      'artifactPathValidation' => NULL,
      'log_output_file' => NULL,
      'enable_font_subsetting' => false,
      'pdf_backend' => 'CPDF',
      'default_media_type' => 'screen',
      'default_paper_size' => 'a4',
      'default_paper_orientation' => 'portrait',
      'default_font' => 'serif',
      'dpi' => 96,
      'enable_php' => false,
      'enable_javascript' => true,
      'enable_remote' => false,
      'allowed_remote_hosts' => NULL,
      'font_height_ratio' => 1.1,
      'enable_html5_parser' => true,
    ),
  ),
  'scribe' => 
  array (
    'title' => 'Your App API Documentation',
    'description' => '',
    'base_url' => 'http://localhost/main',
    'routes' => 
    array (
      0 => 
      array (
        'match' => 
        array (
          'prefixes' => 
          array (
            0 => 'api/*',
          ),
          'domains' => 
          array (
            0 => '*',
          ),
        ),
        'include' => 
        array (
        ),
        'exclude' => 
        array (
        ),
      ),
    ),
    'type' => 'static',
    'theme' => 'default',
    'static' => 
    array (
      'output_path' => 'public/docs',
    ),
    'laravel' => 
    array (
      'add_routes' => true,
      'docs_url' => '/docs',
      'assets_directory' => NULL,
      'middleware' => 
      array (
      ),
    ),
    'external' => 
    array (
      'html_attributes' => 
      array (
      ),
    ),
    'try_it_out' => 
    array (
      'enabled' => true,
      'base_url' => NULL,
      'use_csrf' => false,
      'csrf_url' => '/sanctum/csrf-cookie',
    ),
    'auth' => 
    array (
      'enabled' => false,
      'default' => false,
      'in' => 'bearer',
      'name' => 'key',
      'use_value' => NULL,
      'placeholder' => '{YOUR_AUTH_KEY}',
      'extra_info' => 'You can retrieve your token by visiting your dashboard and clicking <b>Generate API token</b>.',
    ),
    'intro_text' => '    This documentation aims to provide all the information you need to work with our API.

    <aside>As you scroll, you\'ll see code examples for working with the API in different programming languages in the dark area to the right (or as part of the content on mobile).
    You can switch the language used with the tabs at the top right (or from the nav menu at the top left on mobile).</aside>',
    'example_languages' => 
    array (
      0 => 'bash',
      1 => 'javascript',
    ),
    'postman' => 
    array (
      'enabled' => true,
      'overrides' => 
      array (
      ),
    ),
    'openapi' => 
    array (
      'enabled' => true,
      'overrides' => 
      array (
      ),
      'generators' => 
      array (
      ),
    ),
    'groups' => 
    array (
      'default' => 'Endpoints',
      'order' => 
      array (
      ),
    ),
    'logo' => false,
    'last_updated' => 'Last updated: {date:F j, Y}',
    'examples' => 
    array (
      'faker_seed' => 1234,
      'models_source' => 
      array (
        0 => 'factoryCreate',
        1 => 'factoryMake',
        2 => 'databaseFirst',
      ),
    ),
    'strategies' => 
    array (
      'metadata' => 
      array (
        0 => 'Knuckles\\Scribe\\Extracting\\Strategies\\Metadata\\GetFromDocBlocks',
        1 => 'Knuckles\\Scribe\\Extracting\\Strategies\\Metadata\\GetFromMetadataAttributes',
      ),
      'headers' => 
      array (
        0 => 'Knuckles\\Scribe\\Extracting\\Strategies\\Headers\\GetFromHeaderAttribute',
        1 => 'Knuckles\\Scribe\\Extracting\\Strategies\\Headers\\GetFromHeaderTag',
        2 => 
        array (
          0 => 'Knuckles\\Scribe\\Extracting\\Strategies\\StaticData',
          1 => 
          array (
            'only' => 
            array (
            ),
            'except' => 
            array (
            ),
            'data' => 
            array (
              'Content-Type' => 'application/json',
              'Accept' => 'application/json',
            ),
          ),
        ),
      ),
      'urlParameters' => 
      array (
        0 => 'Knuckles\\Scribe\\Extracting\\Strategies\\UrlParameters\\GetFromLaravelAPI',
        1 => 'Knuckles\\Scribe\\Extracting\\Strategies\\UrlParameters\\GetFromUrlParamAttribute',
        2 => 'Knuckles\\Scribe\\Extracting\\Strategies\\UrlParameters\\GetFromUrlParamTag',
      ),
      'queryParameters' => 
      array (
        0 => 'Knuckles\\Scribe\\Extracting\\Strategies\\QueryParameters\\GetFromFormRequest',
        1 => 'Knuckles\\Scribe\\Extracting\\Strategies\\QueryParameters\\GetFromInlineValidator',
        2 => 'Knuckles\\Scribe\\Extracting\\Strategies\\QueryParameters\\GetFromQueryParamAttribute',
        3 => 'Knuckles\\Scribe\\Extracting\\Strategies\\QueryParameters\\GetFromQueryParamTag',
      ),
      'bodyParameters' => 
      array (
        0 => 'Knuckles\\Scribe\\Extracting\\Strategies\\BodyParameters\\GetFromFormRequest',
        1 => 'Knuckles\\Scribe\\Extracting\\Strategies\\BodyParameters\\GetFromInlineValidator',
        2 => 'Knuckles\\Scribe\\Extracting\\Strategies\\BodyParameters\\GetFromBodyParamAttribute',
        3 => 'Knuckles\\Scribe\\Extracting\\Strategies\\BodyParameters\\GetFromBodyParamTag',
      ),
      'responses' => 
      array (
        0 => 'Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseResponseAttributes',
        1 => 'Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseTransformerTags',
        2 => 'Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseApiResourceTags',
        3 => 'Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseResponseTag',
        4 => 'Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\UseResponseFileTag',
        5 => 
        array (
          0 => 'Knuckles\\Scribe\\Extracting\\Strategies\\Responses\\ResponseCalls',
          1 => 
          array (
            'only' => 
            array (
              0 => 'GET *',
            ),
            'except' => 
            array (
            ),
            'config' => 
            array (
              'app.debug' => false,
            ),
            'queryParams' => 
            array (
            ),
            'bodyParams' => 
            array (
            ),
            'fileParams' => 
            array (
            ),
            'cookies' => 
            array (
            ),
          ),
        ),
      ),
      'responseFields' => 
      array (
        0 => 'Knuckles\\Scribe\\Extracting\\Strategies\\ResponseFields\\GetFromResponseFieldAttribute',
        1 => 'Knuckles\\Scribe\\Extracting\\Strategies\\ResponseFields\\GetFromResponseFieldTag',
      ),
    ),
    'database_connections_to_transact' => 
    array (
      0 => 'mysql',
    ),
    'fractal' => 
    array (
      'serializer' => NULL,
    ),
    'assets_directory' => 'vendor/core/packages/api',
  ),
  'laravel-form-builder' => 
  array (
    'defaults' => 
    array (
      'wrapper_class' => 'form-group',
      'wrapper_error_class' => 'has-error',
      'label_class' => 'control-label',
      'field_class' => 'form-control',
      'field_error_class' => '',
      'help_block_class' => 'help-block',
      'error_class' => 'text-danger',
      'required_class' => 'required',
      'help_block_tag' => 'p',
    ),
    'form' => 'laravel-form-builder::form',
    'text' => 'laravel-form-builder::text',
    'textarea' => 'laravel-form-builder::textarea',
    'button' => 'laravel-form-builder::button',
    'buttongroup' => 'laravel-form-builder::buttongroup',
    'radio' => 'laravel-form-builder::radio',
    'checkbox' => 'laravel-form-builder::checkbox',
    'select' => 'laravel-form-builder::select',
    'choice' => 'laravel-form-builder::choice',
    'repeated' => 'laravel-form-builder::repeated',
    'child_form' => 'laravel-form-builder::child_form',
    'collection' => 'laravel-form-builder::collection',
    'static' => 'laravel-form-builder::static',
    'template_prefix' => '',
    'default_namespace' => '',
    'custom_fields' => 
    array (
    ),
    'plain_form_class' => 'Botble\\Base\\Forms\\Form',
    'form_builder_class' => 'Botble\\Base\\Forms\\FormBuilder',
    'form_helper_class' => 'Botble\\Base\\Forms\\FormHelper',
    'defaults.wrapper_class' => 'mb-3 position-relative',
    'defaults.label_class' => 'form-label',
    'defaults.field_error_class' => 'is-invalid',
    'defaults.help_block_class' => 'form-hint',
    'defaults.error_class' => 'invalid-feedback',
    'defaults.help_block_tag' => 'small',
    'defaults.select' => 
    array (
      'field_class' => 'form-select',
    ),
  ),
  'core' => 
  array (
    'base' => 
    array (
      'general' => 
      array (
        'admin_dir' => 'admin',
        'base_name' => 'Your App',
        'logo' => '/vendor/core/core/base/images/logo.png',
        'favicon' => '/vendor/core/core/base/images/favicon.png',
        'editor' => 
        array (
          'ckeditor' => 
          array (
            'js' => 
            array (
              0 => '/vendor/core/core/base/libraries/ckeditor/ckeditor.js',
            ),
          ),
          'tinymce' => 
          array (
            'js' => 
            array (
              0 => '/vendor/core/core/base/libraries/tinymce/tinymce.min.js',
            ),
          ),
          'primary' => 'ckeditor',
        ),
        'error_reporting' => 
        array (
          'to' => NULL,
          'via_slack' => false,
          'ignored_bots' => 
          array (
            0 => 'googlebot',
            1 => 'bingbot',
            2 => 'slurp',
            3 => 'ia_archiver',
          ),
        ),
        'enable_https_support' => false,
        'force_root_url' => NULL,
        'force_schema' => NULL,
        'max_execution_time' => 300,
        'memory_limit' => NULL,
        'date_format' => 
        array (
          'date' => 'Y-m-d',
          'date_time' => 'Y-m-d H:i:s',
          'js' => 
          array (
            'date' => 'yyyy-mm-dd',
            'date_time' => 'yyyy-mm-dd H:i:s',
          ),
        ),
        'locale' => 'en',
        'demo' => 
        array (
          'account' => 
          array (
            'username' => 'admin',
            'password' => '********',
          ),
        ),
        'google_fonts' => 
        array (
          0 => '42dot Sans',
          1 => 'ABeeZee',
          2 => 'ADLaM Display',
          3 => 'AR One Sans',
          4 => 'Abel',
          5 => 'Abhaya Libre',
          6 => 'Aboreto',
          7 => 'Abril Fatface',
          8 => 'Abyssinica SIL',
          9 => 'Aclonica',
          10 => 'Acme',
          11 => 'Actor',
          12 => 'Adamina',
          13 => 'Advent Pro',
          14 => 'Afacad',
          15 => 'Afacad Flux',
          16 => 'Agbalumo',
          17 => 'Agdasima',
          18 => 'Agu Display',
          19 => 'Aguafina Script',
          20 => 'Akatab',
          21 => 'Akaya Kanadaka',
          22 => 'Akaya Telivigala',
          23 => 'Akronim',
          24 => 'Akshar',
          25 => 'Aladin',
          26 => 'Alata',
          27 => 'Alatsi',
          28 => 'Albert Sans',
          29 => 'Aldrich',
          30 => 'Alef',
          31 => 'Alegreya',
          32 => 'Alegreya SC',
          33 => 'Alegreya Sans',
          34 => 'Alegreya Sans SC',
          35 => 'Aleo',
          36 => 'Alex Brush',
          37 => 'Alexandria',
          38 => 'Alfa Slab One',
          39 => 'Alice',
          40 => 'Alike',
          41 => 'Alike Angular',
          42 => 'Alkalami',
          43 => 'Alkatra',
          44 => 'Allan',
          45 => 'Allerta',
          46 => 'Allerta Stencil',
          47 => 'Allison',
          48 => 'Allura',
          49 => 'Almarai',
          50 => 'Almendra',
          51 => 'Almendra Display',
          52 => 'Almendra SC',
          53 => 'Alumni Sans',
          54 => 'Alumni Sans Collegiate One',
          55 => 'Alumni Sans Inline One',
          56 => 'Alumni Sans Pinstripe',
          57 => 'Amarante',
          58 => 'Amaranth',
          59 => 'Amatic SC',
          60 => 'Amethysta',
          61 => 'Amiko',
          62 => 'Amiri',
          63 => 'Amiri Quran',
          64 => 'Amita',
          65 => 'Anaheim',
          66 => 'Andada Pro',
          67 => 'Andika',
          68 => 'Anek Bangla',
          69 => 'Anek Devanagari',
          70 => 'Anek Gujarati',
          71 => 'Anek Gurmukhi',
          72 => 'Anek Kannada',
          73 => 'Anek Latin',
          74 => 'Anek Malayalam',
          75 => 'Anek Odia',
          76 => 'Anek Tamil',
          77 => 'Anek Telugu',
          78 => 'Angkor',
          79 => 'Annapurna SIL',
          80 => 'Annie Use Your Telescope',
          81 => 'Anonymous Pro',
          82 => 'Anta',
          83 => 'Antic',
          84 => 'Antic Didone',
          85 => 'Antic Slab',
          86 => 'Anton',
          87 => 'Anton SC',
          88 => 'Antonio',
          89 => 'Anuphan',
          90 => 'Anybody',
          91 => 'Aoboshi One',
          92 => 'Arapey',
          93 => 'Arbutus',
          94 => 'Arbutus Slab',
          95 => 'Architects Daughter',
          96 => 'Archivo',
          97 => 'Archivo Black',
          98 => 'Archivo Narrow',
          99 => 'Are You Serious',
          100 => 'Aref Ruqaa',
          101 => 'Aref Ruqaa Ink',
          102 => 'Arima',
          103 => 'Arimo',
          104 => 'Arizonia',
          105 => 'Armata',
          106 => 'Arsenal',
          107 => 'Arsenal SC',
          108 => 'Artifika',
          109 => 'Arvo',
          110 => 'Arya',
          111 => 'Asap',
          112 => 'Asap Condensed',
          113 => 'Asar',
          114 => 'Asset',
          115 => 'Assistant',
          116 => 'Astloch',
          117 => 'Asul',
          118 => 'Athiti',
          119 => 'Atkinson Hyperlegible',
          120 => 'Atkinson Hyperlegible Mono',
          121 => 'Atkinson Hyperlegible Next',
          122 => 'Atma',
          123 => 'Atomic Age',
          124 => 'Aubrey',
          125 => 'Audiowide',
          126 => 'Autour One',
          127 => 'Average',
          128 => 'Average Sans',
          129 => 'Averia Gruesa Libre',
          130 => 'Averia Libre',
          131 => 'Averia Sans Libre',
          132 => 'Averia Serif Libre',
          133 => 'Azeret Mono',
          134 => 'B612',
          135 => 'B612 Mono',
          136 => 'BIZ UDGothic',
          137 => 'BIZ UDMincho',
          138 => 'BIZ UDPGothic',
          139 => 'BIZ UDPMincho',
          140 => 'Babylonica',
          141 => 'Bacasime Antique',
          142 => 'Bad Script',
          143 => 'Badeen Display',
          144 => 'Bagel Fat One',
          145 => 'Bahiana',
          146 => 'Bahianita',
          147 => 'Bai Jamjuree',
          148 => 'Bakbak One',
          149 => 'Ballet',
          150 => 'Baloo 2',
          151 => 'Baloo Bhai 2',
          152 => 'Baloo Bhaijaan 2',
          153 => 'Baloo Bhaina 2',
          154 => 'Baloo Chettan 2',
          155 => 'Baloo Da 2',
          156 => 'Baloo Paaji 2',
          157 => 'Baloo Tamma 2',
          158 => 'Baloo Tammudu 2',
          159 => 'Baloo Thambi 2',
          160 => 'Balsamiq Sans',
          161 => 'Balthazar',
          162 => 'Bangers',
          163 => 'Barlow',
          164 => 'Barlow Condensed',
          165 => 'Barlow Semi Condensed',
          166 => 'Barriecito',
          167 => 'Barrio',
          168 => 'Basic',
          169 => 'Baskervville',
          170 => 'Baskervville SC',
          171 => 'Battambang',
          172 => 'Baumans',
          173 => 'Bayon',
          174 => 'Be Vietnam Pro',
          175 => 'Beau Rivage',
          176 => 'Bebas Neue',
          177 => 'Beiruti',
          178 => 'Belanosima',
          179 => 'Belgrano',
          180 => 'Bellefair',
          181 => 'Belleza',
          182 => 'Bellota',
          183 => 'Bellota Text',
          184 => 'BenchNine',
          185 => 'Benne',
          186 => 'Bentham',
          187 => 'Berkshire Swash',
          188 => 'Besley',
          189 => 'Beth Ellen',
          190 => 'Bevan',
          191 => 'BhuTuka Expanded One',
          192 => 'Big Shoulders',
          193 => 'Big Shoulders Inline',
          194 => 'Big Shoulders Stencil',
          195 => 'Bigelow Rules',
          196 => 'Bigshot One',
          197 => 'Bilbo',
          198 => 'Bilbo Swash Caps',
          199 => 'BioRhyme',
          200 => 'BioRhyme Expanded',
          201 => 'Birthstone',
          202 => 'Birthstone Bounce',
          203 => 'Biryani',
          204 => 'Bitter',
          205 => 'Black And White Picture',
          206 => 'Black Han Sans',
          207 => 'Black Ops One',
          208 => 'Blaka',
          209 => 'Blaka Hollow',
          210 => 'Blaka Ink',
          211 => 'Blinker',
          212 => 'Bodoni Moda',
          213 => 'Bodoni Moda SC',
          214 => 'Bokor',
          215 => 'Boldonse',
          216 => 'Bona Nova',
          217 => 'Bona Nova SC',
          218 => 'Bonbon',
          219 => 'Bonheur Royale',
          220 => 'Boogaloo',
          221 => 'Borel',
          222 => 'Bowlby One',
          223 => 'Bowlby One SC',
          224 => 'Braah One',
          225 => 'Brawler',
          226 => 'Bree Serif',
          227 => 'Bricolage Grotesque',
          228 => 'Bruno Ace',
          229 => 'Bruno Ace SC',
          230 => 'Brygada 1918',
          231 => 'Bubblegum Sans',
          232 => 'Bubbler One',
          233 => 'Buda',
          234 => 'Buenard',
          235 => 'Bungee',
          236 => 'Bungee Hairline',
          237 => 'Bungee Inline',
          238 => 'Bungee Outline',
          239 => 'Bungee Shade',
          240 => 'Bungee Spice',
          241 => 'Bungee Tint',
          242 => 'Butcherman',
          243 => 'Butterfly Kids',
          244 => 'Bytesized',
          245 => 'Cabin',
          246 => 'Cabin Condensed',
          247 => 'Cabin Sketch',
          248 => 'Cactus Classical Serif',
          249 => 'Caesar Dressing',
          250 => 'Cagliostro',
          251 => 'Cairo',
          252 => 'Cairo Play',
          253 => 'Cal Sans',
          254 => 'Caladea',
          255 => 'Calistoga',
          256 => 'Calligraffitti',
          257 => 'Cambay',
          258 => 'Cambo',
          259 => 'Candal',
          260 => 'Cantarell',
          261 => 'Cantata One',
          262 => 'Cantora One',
          263 => 'Caprasimo',
          264 => 'Capriola',
          265 => 'Caramel',
          266 => 'Carattere',
          267 => 'Cardo',
          268 => 'Carlito',
          269 => 'Carme',
          270 => 'Carrois Gothic',
          271 => 'Carrois Gothic SC',
          272 => 'Carter One',
          273 => 'Cascadia Code',
          274 => 'Cascadia Mono',
          275 => 'Castoro',
          276 => 'Castoro Titling',
          277 => 'Catamaran',
          278 => 'Caudex',
          279 => 'Caveat',
          280 => 'Caveat Brush',
          281 => 'Cedarville Cursive',
          282 => 'Ceviche One',
          283 => 'Chakra Petch',
          284 => 'Changa',
          285 => 'Changa One',
          286 => 'Chango',
          287 => 'Charis SIL',
          288 => 'Charm',
          289 => 'Charmonman',
          290 => 'Chathura',
          291 => 'Chau Philomene One',
          292 => 'Chela One',
          293 => 'Chelsea Market',
          294 => 'Chenla',
          295 => 'Cherish',
          296 => 'Cherry Bomb One',
          297 => 'Cherry Cream Soda',
          298 => 'Cherry Swash',
          299 => 'Chewy',
          300 => 'Chicle',
          301 => 'Chilanka',
          302 => 'Chivo',
          303 => 'Chivo Mono',
          304 => 'Chocolate Classical Sans',
          305 => 'Chokokutai',
          306 => 'Chonburi',
          307 => 'Cinzel',
          308 => 'Cinzel Decorative',
          309 => 'Clicker Script',
          310 => 'Climate Crisis',
          311 => 'Coda',
          312 => 'Codystar',
          313 => 'Coiny',
          314 => 'Combo',
          315 => 'Comfortaa',
          316 => 'Comforter',
          317 => 'Comforter Brush',
          318 => 'Comic Neue',
          319 => 'Comic Relief',
          320 => 'Coming Soon',
          321 => 'Comme',
          322 => 'Commissioner',
          323 => 'Concert One',
          324 => 'Condiment',
          325 => 'Content',
          326 => 'Contrail One',
          327 => 'Convergence',
          328 => 'Cookie',
          329 => 'Copse',
          330 => 'Coral Pixels',
          331 => 'Corben',
          332 => 'Corinthia',
          333 => 'Cormorant',
          334 => 'Cormorant Garamond',
          335 => 'Cormorant Infant',
          336 => 'Cormorant SC',
          337 => 'Cormorant Unicase',
          338 => 'Cormorant Upright',
          339 => 'Courgette',
          340 => 'Courier Prime',
          341 => 'Cousine',
          342 => 'Coustard',
          343 => 'Covered By Your Grace',
          344 => 'Crafty Girls',
          345 => 'Creepster',
          346 => 'Crete Round',
          347 => 'Crimson Pro',
          348 => 'Crimson Text',
          349 => 'Croissant One',
          350 => 'Crushed',
          351 => 'Cuprum',
          352 => 'Cute Font',
          353 => 'Cutive',
          354 => 'Cutive Mono',
          355 => 'DM Mono',
          356 => 'DM Sans',
          357 => 'DM Serif Display',
          358 => 'DM Serif Text',
          359 => 'Dai Banna SIL',
          360 => 'Damion',
          361 => 'Dancing Script',
          362 => 'Danfo',
          363 => 'Dangrek',
          364 => 'Darker Grotesque',
          365 => 'Darumadrop One',
          366 => 'David Libre',
          367 => 'Dawning of a New Day',
          368 => 'Days One',
          369 => 'Dekko',
          370 => 'Dela Gothic One',
          371 => 'Delicious Handrawn',
          372 => 'Delius',
          373 => 'Delius Swash Caps',
          374 => 'Delius Unicase',
          375 => 'Della Respira',
          376 => 'Denk One',
          377 => 'Devonshire',
          378 => 'Dhurjati',
          379 => 'Didact Gothic',
          380 => 'Diphylleia',
          381 => 'Diplomata',
          382 => 'Diplomata SC',
          383 => 'Do Hyeon',
          384 => 'Dokdo',
          385 => 'Domine',
          386 => 'Donegal One',
          387 => 'Dongle',
          388 => 'Doppio One',
          389 => 'Dorsa',
          390 => 'Dosis',
          391 => 'DotGothic16',
          392 => 'Doto',
          393 => 'Dr Sugiyama',
          394 => 'Duru Sans',
          395 => 'DynaPuff',
          396 => 'Dynalight',
          397 => 'EB Garamond',
          398 => 'Eagle Lake',
          399 => 'East Sea Dokdo',
          400 => 'Eater',
          401 => 'Economica',
          402 => 'Eczar',
          403 => 'Edu AU VIC WA NT Arrows',
          404 => 'Edu AU VIC WA NT Dots',
          405 => 'Edu AU VIC WA NT Guides',
          406 => 'Edu AU VIC WA NT Hand',
          407 => 'Edu AU VIC WA NT Pre',
          408 => 'Edu NSW ACT Foundation',
          409 => 'Edu QLD Beginner',
          410 => 'Edu SA Beginner',
          411 => 'Edu TAS Beginner',
          412 => 'Edu VIC WA NT Beginner',
          413 => 'El Messiri',
          414 => 'Electrolize',
          415 => 'Elsie',
          416 => 'Elsie Swash Caps',
          417 => 'Emblema One',
          418 => 'Emilys Candy',
          419 => 'Encode Sans',
          420 => 'Encode Sans Condensed',
          421 => 'Encode Sans Expanded',
          422 => 'Encode Sans SC',
          423 => 'Encode Sans Semi Condensed',
          424 => 'Encode Sans Semi Expanded',
          425 => 'Engagement',
          426 => 'Englebert',
          427 => 'Enriqueta',
          428 => 'Ephesis',
          429 => 'Epilogue',
          430 => 'Erica One',
          431 => 'Esteban',
          432 => 'Estonia',
          433 => 'Euphoria Script',
          434 => 'Ewert',
          435 => 'Exo',
          436 => 'Exo 2',
          437 => 'Expletus Sans',
          438 => 'Explora',
          439 => 'Faculty Glyphic',
          440 => 'Fahkwang',
          441 => 'Familjen Grotesk',
          442 => 'Fanwood Text',
          443 => 'Farro',
          444 => 'Farsan',
          445 => 'Fascinate',
          446 => 'Fascinate Inline',
          447 => 'Faster One',
          448 => 'Fasthand',
          449 => 'Fauna One',
          450 => 'Faustina',
          451 => 'Federant',
          452 => 'Federo',
          453 => 'Felipa',
          454 => 'Fenix',
          455 => 'Festive',
          456 => 'Figtree',
          457 => 'Finger Paint',
          458 => 'Finlandica',
          459 => 'Fira Code',
          460 => 'Fira Mono',
          461 => 'Fira Sans',
          462 => 'Fira Sans Condensed',
          463 => 'Fira Sans Extra Condensed',
          464 => 'Fjalla One',
          465 => 'Fjord One',
          466 => 'Flamenco',
          467 => 'Flavors',
          468 => 'Fleur De Leah',
          469 => 'Flow Block',
          470 => 'Flow Circular',
          471 => 'Flow Rounded',
          472 => 'Foldit',
          473 => 'Fondamento',
          474 => 'Fontdiner Swanky',
          475 => 'Forum',
          476 => 'Fragment Mono',
          477 => 'Francois One',
          478 => 'Frank Ruhl Libre',
          479 => 'Fraunces',
          480 => 'Freckle Face',
          481 => 'Fredericka the Great',
          482 => 'Fredoka',
          483 => 'Freehand',
          484 => 'Freeman',
          485 => 'Fresca',
          486 => 'Frijole',
          487 => 'Fruktur',
          488 => 'Fugaz One',
          489 => 'Fuggles',
          490 => 'Funnel Display',
          491 => 'Funnel Sans',
          492 => 'Fustat',
          493 => 'Fuzzy Bubbles',
          494 => 'GFS Didot',
          495 => 'GFS Neohellenic',
          496 => 'Ga Maamli',
          497 => 'Gabarito',
          498 => 'Gabriela',
          499 => 'Gaegu',
          500 => 'Gafata',
          501 => 'Gajraj One',
          502 => 'Galada',
          503 => 'Galdeano',
          504 => 'Galindo',
          505 => 'Gamja Flower',
          506 => 'Gantari',
          507 => 'Gasoek One',
          508 => 'Gayathri',
          509 => 'Geist',
          510 => 'Geist Mono',
          511 => 'Gelasio',
          512 => 'Gemunu Libre',
          513 => 'Genos',
          514 => 'Gentium Book Plus',
          515 => 'Gentium Plus',
          516 => 'Geo',
          517 => 'Geologica',
          518 => 'Georama',
          519 => 'Geostar',
          520 => 'Geostar Fill',
          521 => 'Germania One',
          522 => 'Gideon Roman',
          523 => 'Gidole',
          524 => 'Gidugu',
          525 => 'Gilda Display',
          526 => 'Girassol',
          527 => 'Give You Glory',
          528 => 'Glass Antiqua',
          529 => 'Glegoo',
          530 => 'Gloock',
          531 => 'Gloria Hallelujah',
          532 => 'Glory',
          533 => 'Gluten',
          534 => 'Goblin One',
          535 => 'Gochi Hand',
          536 => 'Goldman',
          537 => 'Golos Text',
          538 => 'Gorditas',
          539 => 'Gothic A1',
          540 => 'Gotu',
          541 => 'Goudy Bookletter 1911',
          542 => 'Gowun Batang',
          543 => 'Gowun Dodum',
          544 => 'Graduate',
          545 => 'Grand Hotel',
          546 => 'Grandiflora One',
          547 => 'Grandstander',
          548 => 'Grape Nuts',
          549 => 'Gravitas One',
          550 => 'Great Vibes',
          551 => 'Grechen Fuemen',
          552 => 'Grenze',
          553 => 'Grenze Gotisch',
          554 => 'Grey Qo',
          555 => 'Griffy',
          556 => 'Gruppo',
          557 => 'Gudea',
          558 => 'Gugi',
          559 => 'Gulzar',
          560 => 'Gupter',
          561 => 'Gurajada',
          562 => 'Gwendolyn',
          563 => 'Habibi',
          564 => 'Hachi Maru Pop',
          565 => 'Hahmlet',
          566 => 'Halant',
          567 => 'Hammersmith One',
          568 => 'Hanalei',
          569 => 'Hanalei Fill',
          570 => 'Handjet',
          571 => 'Handlee',
          572 => 'Hanken Grotesk',
          573 => 'Hanuman',
          574 => 'Happy Monkey',
          575 => 'Harmattan',
          576 => 'Headland One',
          577 => 'Hedvig Letters Sans',
          578 => 'Hedvig Letters Serif',
          579 => 'Heebo',
          580 => 'Henny Penny',
          581 => 'Hepta Slab',
          582 => 'Herr Von Muellerhoff',
          583 => 'Hi Melody',
          584 => 'Hina Mincho',
          585 => 'Hind',
          586 => 'Hind Guntur',
          587 => 'Hind Madurai',
          588 => 'Hind Mysuru',
          589 => 'Hind Siliguri',
          590 => 'Hind Vadodara',
          591 => 'Holtwood One SC',
          592 => 'Homemade Apple',
          593 => 'Homenaje',
          594 => 'Honk',
          595 => 'Host Grotesk',
          596 => 'Hubballi',
          597 => 'Hubot Sans',
          598 => 'Hurricane',
          599 => 'IBM Plex Mono',
          600 => 'IBM Plex Sans',
          601 => 'IBM Plex Sans Arabic',
          602 => 'IBM Plex Sans Condensed',
          603 => 'IBM Plex Sans Devanagari',
          604 => 'IBM Plex Sans Hebrew',
          605 => 'IBM Plex Sans JP',
          606 => 'IBM Plex Sans KR',
          607 => 'IBM Plex Sans Thai',
          608 => 'IBM Plex Sans Thai Looped',
          609 => 'IBM Plex Serif',
          610 => 'IM Fell DW Pica',
          611 => 'IM Fell DW Pica SC',
          612 => 'IM Fell Double Pica',
          613 => 'IM Fell Double Pica SC',
          614 => 'IM Fell English',
          615 => 'IM Fell English SC',
          616 => 'IM Fell French Canon',
          617 => 'IM Fell French Canon SC',
          618 => 'IM Fell Great Primer',
          619 => 'IM Fell Great Primer SC',
          620 => 'Iansui',
          621 => 'Ibarra Real Nova',
          622 => 'Iceberg',
          623 => 'Iceland',
          624 => 'Imbue',
          625 => 'Imperial Script',
          626 => 'Imprima',
          627 => 'Inclusive Sans',
          628 => 'Inconsolata',
          629 => 'Inder',
          630 => 'Indie Flower',
          631 => 'Ingrid Darling',
          632 => 'Inika',
          633 => 'Inknut Antiqua',
          634 => 'Inria Sans',
          635 => 'Inria Serif',
          636 => 'Inspiration',
          637 => 'Instrument Sans',
          638 => 'Instrument Serif',
          639 => 'Inter',
          640 => 'Inter Tight',
          641 => 'Irish Grover',
          642 => 'Island Moments',
          643 => 'Istok Web',
          644 => 'Italiana',
          645 => 'Italianno',
          646 => 'Itim',
          647 => 'Jacquard 12',
          648 => 'Jacquard 12 Charted',
          649 => 'Jacquard 24',
          650 => 'Jacquard 24 Charted',
          651 => 'Jacquarda Bastarda 9',
          652 => 'Jacquarda Bastarda 9 Charted',
          653 => 'Jacques Francois',
          654 => 'Jacques Francois Shadow',
          655 => 'Jaini',
          656 => 'Jaini Purva',
          657 => 'Jaldi',
          658 => 'Jaro',
          659 => 'Jersey 10',
          660 => 'Jersey 10 Charted',
          661 => 'Jersey 15',
          662 => 'Jersey 15 Charted',
          663 => 'Jersey 20',
          664 => 'Jersey 20 Charted',
          665 => 'Jersey 25',
          666 => 'Jersey 25 Charted',
          667 => 'JetBrains Mono',
          668 => 'Jim Nightshade',
          669 => 'Joan',
          670 => 'Jockey One',
          671 => 'Jolly Lodger',
          672 => 'Jomhuria',
          673 => 'Jomolhari',
          674 => 'Josefin Sans',
          675 => 'Josefin Slab',
          676 => 'Jost',
          677 => 'Joti One',
          678 => 'Jua',
          679 => 'Judson',
          680 => 'Julee',
          681 => 'Julius Sans One',
          682 => 'Junge',
          683 => 'Jura',
          684 => 'Just Another Hand',
          685 => 'Just Me Again Down Here',
          686 => 'K2D',
          687 => 'Kablammo',
          688 => 'Kadwa',
          689 => 'Kaisei Decol',
          690 => 'Kaisei HarunoUmi',
          691 => 'Kaisei Opti',
          692 => 'Kaisei Tokumin',
          693 => 'Kalam',
          694 => 'Kalnia',
          695 => 'Kalnia Glaze',
          696 => 'Kameron',
          697 => 'Kanchenjunga',
          698 => 'Kanit',
          699 => 'Kantumruy Pro',
          700 => 'Karantina',
          701 => 'Karla',
          702 => 'Karla Tamil Inclined',
          703 => 'Karla Tamil Upright',
          704 => 'Karma',
          705 => 'Katibeh',
          706 => 'Kaushan Script',
          707 => 'Kavivanar',
          708 => 'Kavoon',
          709 => 'Kay Pho Du',
          710 => 'Kdam Thmor Pro',
          711 => 'Keania One',
          712 => 'Kelly Slab',
          713 => 'Kenia',
          714 => 'Khand',
          715 => 'Khmer',
          716 => 'Khula',
          717 => 'Kings',
          718 => 'Kirang Haerang',
          719 => 'Kite One',
          720 => 'Kiwi Maru',
          721 => 'Klee One',
          722 => 'Knewave',
          723 => 'KoHo',
          724 => 'Kodchasan',
          725 => 'Kode Mono',
          726 => 'Koh Santepheap',
          727 => 'Kolker Brush',
          728 => 'Konkhmer Sleokchher',
          729 => 'Kosugi',
          730 => 'Kosugi Maru',
          731 => 'Kotta One',
          732 => 'Koulen',
          733 => 'Kranky',
          734 => 'Kreon',
          735 => 'Kristi',
          736 => 'Krona One',
          737 => 'Krub',
          738 => 'Kufam',
          739 => 'Kulim Park',
          740 => 'Kumar One',
          741 => 'Kumar One Outline',
          742 => 'Kumbh Sans',
          743 => 'Kurale',
          744 => 'LXGW WenKai Mono TC',
          745 => 'LXGW WenKai TC',
          746 => 'La Belle Aurore',
          747 => 'Labrada',
          748 => 'Lacquer',
          749 => 'Laila',
          750 => 'Lakki Reddy',
          751 => 'Lalezar',
          752 => 'Lancelot',
          753 => 'Langar',
          754 => 'Lateef',
          755 => 'Lato',
          756 => 'Lavishly Yours',
          757 => 'League Gothic',
          758 => 'League Script',
          759 => 'League Spartan',
          760 => 'Leckerli One',
          761 => 'Ledger',
          762 => 'Lekton',
          763 => 'Lemon',
          764 => 'Lemonada',
          765 => 'Lexend',
          766 => 'Lexend Deca',
          767 => 'Lexend Exa',
          768 => 'Lexend Giga',
          769 => 'Lexend Mega',
          770 => 'Lexend Peta',
          771 => 'Lexend Tera',
          772 => 'Lexend Zetta',
          773 => 'Libre Barcode 128',
          774 => 'Libre Barcode 128 Text',
          775 => 'Libre Barcode 39',
          776 => 'Libre Barcode 39 Extended',
          777 => 'Libre Barcode 39 Extended Text',
          778 => 'Libre Barcode 39 Text',
          779 => 'Libre Barcode EAN13 Text',
          780 => 'Libre Baskerville',
          781 => 'Libre Bodoni',
          782 => 'Libre Caslon Display',
          783 => 'Libre Caslon Text',
          784 => 'Libre Franklin',
          785 => 'Licorice',
          786 => 'Life Savers',
          787 => 'Lilita One',
          788 => 'Lily Script One',
          789 => 'Limelight',
          790 => 'Linden Hill',
          791 => 'Linefont',
          792 => 'Lisu Bosa',
          793 => 'Liter',
          794 => 'Literata',
          795 => 'Liu Jian Mao Cao',
          796 => 'Livvic',
          797 => 'Lobster',
          798 => 'Lobster Two',
          799 => 'Londrina Outline',
          800 => 'Londrina Shadow',
          801 => 'Londrina Sketch',
          802 => 'Londrina Solid',
          803 => 'Long Cang',
          804 => 'Lora',
          805 => 'Love Light',
          806 => 'Love Ya Like A Sister',
          807 => 'Loved by the King',
          808 => 'Lovers Quarrel',
          809 => 'Luckiest Guy',
          810 => 'Lugrasimo',
          811 => 'Lumanosimo',
          812 => 'Lunasima',
          813 => 'Lusitana',
          814 => 'Lustria',
          815 => 'Luxurious Roman',
          816 => 'Luxurious Script',
          817 => 'M PLUS 1',
          818 => 'M PLUS 1 Code',
          819 => 'M PLUS 1p',
          820 => 'M PLUS 2',
          821 => 'M PLUS Code Latin',
          822 => 'M PLUS Rounded 1c',
          823 => 'Ma Shan Zheng',
          824 => 'Macondo',
          825 => 'Macondo Swash Caps',
          826 => 'Mada',
          827 => 'Madimi One',
          828 => 'Magra',
          829 => 'Maiden Orange',
          830 => 'Maitree',
          831 => 'Major Mono Display',
          832 => 'Mako',
          833 => 'Mali',
          834 => 'Mallanna',
          835 => 'Maname',
          836 => 'Mandali',
          837 => 'Manjari',
          838 => 'Manrope',
          839 => 'Mansalva',
          840 => 'Manuale',
          841 => 'Marcellus',
          842 => 'Marcellus SC',
          843 => 'Marck Script',
          844 => 'Margarine',
          845 => 'Marhey',
          846 => 'Markazi Text',
          847 => 'Marko One',
          848 => 'Marmelad',
          849 => 'Martel',
          850 => 'Martel Sans',
          851 => 'Martian Mono',
          852 => 'Marvel',
          853 => 'Mate',
          854 => 'Mate SC',
          855 => 'Matemasie',
          856 => 'Material Icons',
          857 => 'Material Icons Outlined',
          858 => 'Material Icons Round',
          859 => 'Material Icons Sharp',
          860 => 'Material Icons Two Tone',
          861 => 'Material Symbols',
          862 => 'Material Symbols Outlined',
          863 => 'Material Symbols Rounded',
          864 => 'Material Symbols Sharp',
          865 => 'Maven Pro',
          866 => 'McLaren',
          867 => 'Mea Culpa',
          868 => 'Meddon',
          869 => 'MedievalSharp',
          870 => 'Medula One',
          871 => 'Meera Inimai',
          872 => 'Megrim',
          873 => 'Meie Script',
          874 => 'Meow Script',
          875 => 'Merienda',
          876 => 'Merriweather',
          877 => 'Merriweather Sans',
          878 => 'Metal',
          879 => 'Metal Mania',
          880 => 'Metamorphous',
          881 => 'Metrophobic',
          882 => 'Michroma',
          883 => 'Micro 5',
          884 => 'Micro 5 Charted',
          885 => 'Milonga',
          886 => 'Miltonian',
          887 => 'Miltonian Tattoo',
          888 => 'Mina',
          889 => 'Mingzat',
          890 => 'Miniver',
          891 => 'Miriam Libre',
          892 => 'Mirza',
          893 => 'Miss Fajardose',
          894 => 'Mitr',
          895 => 'Mochiy Pop One',
          896 => 'Mochiy Pop P One',
          897 => 'Modak',
          898 => 'Modern Antiqua',
          899 => 'Moderustic',
          900 => 'Mogra',
          901 => 'Mohave',
          902 => 'Moirai One',
          903 => 'Molengo',
          904 => 'Molle',
          905 => 'Mona Sans',
          906 => 'Monda',
          907 => 'Monofett',
          908 => 'Monomakh',
          909 => 'Monomaniac One',
          910 => 'Monoton',
          911 => 'Monsieur La Doulaise',
          912 => 'Montaga',
          913 => 'Montagu Slab',
          914 => 'MonteCarlo',
          915 => 'Montez',
          916 => 'Montserrat',
          917 => 'Montserrat Alternates',
          918 => 'Montserrat Underline',
          919 => 'Moo Lah Lah',
          920 => 'Mooli',
          921 => 'Moon Dance',
          922 => 'Moul',
          923 => 'Moulpali',
          924 => 'Mountains of Christmas',
          925 => 'Mouse Memoirs',
          926 => 'Mr Bedfort',
          927 => 'Mr Dafoe',
          928 => 'Mr De Haviland',
          929 => 'Mrs Saint Delafield',
          930 => 'Mrs Sheppards',
          931 => 'Ms Madi',
          932 => 'Mukta',
          933 => 'Mukta Mahee',
          934 => 'Mukta Malar',
          935 => 'Mukta Vaani',
          936 => 'Mulish',
          937 => 'Murecho',
          938 => 'MuseoModerno',
          939 => 'My Soul',
          940 => 'Mynerve',
          941 => 'Mystery Quest',
          942 => 'NTR',
          943 => 'Nabla',
          944 => 'Namdhinggo',
          945 => 'Nanum Brush Script',
          946 => 'Nanum Gothic',
          947 => 'Nanum Gothic Coding',
          948 => 'Nanum Myeongjo',
          949 => 'Nanum Pen Script',
          950 => 'Narnoor',
          951 => 'National Park',
          952 => 'Neonderthaw',
          953 => 'Nerko One',
          954 => 'Neucha',
          955 => 'Neuton',
          956 => 'New Amsterdam',
          957 => 'New Rocker',
          958 => 'New Tegomin',
          959 => 'News Cycle',
          960 => 'Newsreader',
          961 => 'Niconne',
          962 => 'Niramit',
          963 => 'Nixie One',
          964 => 'Nobile',
          965 => 'Nokora',
          966 => 'Norican',
          967 => 'Nosifer',
          968 => 'Notable',
          969 => 'Nothing You Could Do',
          970 => 'Noticia Text',
          971 => 'Noto Color Emoji',
          972 => 'Noto Emoji',
          973 => 'Noto Kufi Arabic',
          974 => 'Noto Music',
          975 => 'Noto Naskh Arabic',
          976 => 'Noto Nastaliq Urdu',
          977 => 'Noto Rashi Hebrew',
          978 => 'Noto Sans',
          979 => 'Noto Sans Adlam',
          980 => 'Noto Sans Adlam Unjoined',
          981 => 'Noto Sans Anatolian Hieroglyphs',
          982 => 'Noto Sans Arabic',
          983 => 'Noto Sans Armenian',
          984 => 'Noto Sans Avestan',
          985 => 'Noto Sans Balinese',
          986 => 'Noto Sans Bamum',
          987 => 'Noto Sans Bassa Vah',
          988 => 'Noto Sans Batak',
          989 => 'Noto Sans Bengali',
          990 => 'Noto Sans Bhaiksuki',
          991 => 'Noto Sans Brahmi',
          992 => 'Noto Sans Buginese',
          993 => 'Noto Sans Buhid',
          994 => 'Noto Sans Canadian Aboriginal',
          995 => 'Noto Sans Carian',
          996 => 'Noto Sans Caucasian Albanian',
          997 => 'Noto Sans Chakma',
          998 => 'Noto Sans Cham',
          999 => 'Noto Sans Cherokee',
          1000 => 'Noto Sans Chorasmian',
          1001 => 'Noto Sans Coptic',
          1002 => 'Noto Sans Cuneiform',
          1003 => 'Noto Sans Cypriot',
          1004 => 'Noto Sans Cypro Minoan',
          1005 => 'Noto Sans Deseret',
          1006 => 'Noto Sans Devanagari',
          1007 => 'Noto Sans Display',
          1008 => 'Noto Sans Duployan',
          1009 => 'Noto Sans Egyptian Hieroglyphs',
          1010 => 'Noto Sans Elbasan',
          1011 => 'Noto Sans Elymaic',
          1012 => 'Noto Sans Ethiopic',
          1013 => 'Noto Sans Georgian',
          1014 => 'Noto Sans Glagolitic',
          1015 => 'Noto Sans Gothic',
          1016 => 'Noto Sans Grantha',
          1017 => 'Noto Sans Gujarati',
          1018 => 'Noto Sans Gunjala Gondi',
          1019 => 'Noto Sans Gurmukhi',
          1020 => 'Noto Sans HK',
          1021 => 'Noto Sans Hanifi Rohingya',
          1022 => 'Noto Sans Hanunoo',
          1023 => 'Noto Sans Hatran',
          1024 => 'Noto Sans Hebrew',
          1025 => 'Noto Sans Imperial Aramaic',
          1026 => 'Noto Sans Indic Siyaq Numbers',
          1027 => 'Noto Sans Inscriptional Pahlavi',
          1028 => 'Noto Sans Inscriptional Parthian',
          1029 => 'Noto Sans JP',
          1030 => 'Noto Sans Javanese',
          1031 => 'Noto Sans KR',
          1032 => 'Noto Sans Kaithi',
          1033 => 'Noto Sans Kannada',
          1034 => 'Noto Sans Kawi',
          1035 => 'Noto Sans Kayah Li',
          1036 => 'Noto Sans Kharoshthi',
          1037 => 'Noto Sans Khmer',
          1038 => 'Noto Sans Khojki',
          1039 => 'Noto Sans Khudawadi',
          1040 => 'Noto Sans Lao',
          1041 => 'Noto Sans Lao Looped',
          1042 => 'Noto Sans Lepcha',
          1043 => 'Noto Sans Limbu',
          1044 => 'Noto Sans Linear A',
          1045 => 'Noto Sans Linear B',
          1046 => 'Noto Sans Lisu',
          1047 => 'Noto Sans Lycian',
          1048 => 'Noto Sans Lydian',
          1049 => 'Noto Sans Mahajani',
          1050 => 'Noto Sans Malayalam',
          1051 => 'Noto Sans Mandaic',
          1052 => 'Noto Sans Manichaean',
          1053 => 'Noto Sans Marchen',
          1054 => 'Noto Sans Masaram Gondi',
          1055 => 'Noto Sans Math',
          1056 => 'Noto Sans Mayan Numerals',
          1057 => 'Noto Sans Medefaidrin',
          1058 => 'Noto Sans Meetei Mayek',
          1059 => 'Noto Sans Mende Kikakui',
          1060 => 'Noto Sans Meroitic',
          1061 => 'Noto Sans Miao',
          1062 => 'Noto Sans Modi',
          1063 => 'Noto Sans Mongolian',
          1064 => 'Noto Sans Mono',
          1065 => 'Noto Sans Mro',
          1066 => 'Noto Sans Multani',
          1067 => 'Noto Sans Myanmar',
          1068 => 'Noto Sans NKo',
          1069 => 'Noto Sans NKo Unjoined',
          1070 => 'Noto Sans Nabataean',
          1071 => 'Noto Sans Nag Mundari',
          1072 => 'Noto Sans Nandinagari',
          1073 => 'Noto Sans New Tai Lue',
          1074 => 'Noto Sans Newa',
          1075 => 'Noto Sans Nushu',
          1076 => 'Noto Sans Ogham',
          1077 => 'Noto Sans Ol Chiki',
          1078 => 'Noto Sans Old Hungarian',
          1079 => 'Noto Sans Old Italic',
          1080 => 'Noto Sans Old North Arabian',
          1081 => 'Noto Sans Old Permic',
          1082 => 'Noto Sans Old Persian',
          1083 => 'Noto Sans Old Sogdian',
          1084 => 'Noto Sans Old South Arabian',
          1085 => 'Noto Sans Old Turkic',
          1086 => 'Noto Sans Oriya',
          1087 => 'Noto Sans Osage',
          1088 => 'Noto Sans Osmanya',
          1089 => 'Noto Sans Pahawh Hmong',
          1090 => 'Noto Sans Palmyrene',
          1091 => 'Noto Sans Pau Cin Hau',
          1092 => 'Noto Sans PhagsPa',
          1093 => 'Noto Sans Phoenician',
          1094 => 'Noto Sans Psalter Pahlavi',
          1095 => 'Noto Sans Rejang',
          1096 => 'Noto Sans Runic',
          1097 => 'Noto Sans SC',
          1098 => 'Noto Sans Samaritan',
          1099 => 'Noto Sans Saurashtra',
          1100 => 'Noto Sans Sharada',
          1101 => 'Noto Sans Shavian',
          1102 => 'Noto Sans Siddham',
          1103 => 'Noto Sans SignWriting',
          1104 => 'Noto Sans Sinhala',
          1105 => 'Noto Sans Sogdian',
          1106 => 'Noto Sans Sora Sompeng',
          1107 => 'Noto Sans Soyombo',
          1108 => 'Noto Sans Sundanese',
          1109 => 'Noto Sans Syloti Nagri',
          1110 => 'Noto Sans Symbols',
          1111 => 'Noto Sans Symbols 2',
          1112 => 'Noto Sans Syriac',
          1113 => 'Noto Sans Syriac Eastern',
          1114 => 'Noto Sans TC',
          1115 => 'Noto Sans Tagalog',
          1116 => 'Noto Sans Tagbanwa',
          1117 => 'Noto Sans Tai Le',
          1118 => 'Noto Sans Tai Tham',
          1119 => 'Noto Sans Tai Viet',
          1120 => 'Noto Sans Takri',
          1121 => 'Noto Sans Tamil',
          1122 => 'Noto Sans Tamil Supplement',
          1123 => 'Noto Sans Tangsa',
          1124 => 'Noto Sans Telugu',
          1125 => 'Noto Sans Thaana',
          1126 => 'Noto Sans Thai',
          1127 => 'Noto Sans Thai Looped',
          1128 => 'Noto Sans Tifinagh',
          1129 => 'Noto Sans Tirhuta',
          1130 => 'Noto Sans Ugaritic',
          1131 => 'Noto Sans Vai',
          1132 => 'Noto Sans Vithkuqi',
          1133 => 'Noto Sans Wancho',
          1134 => 'Noto Sans Warang Citi',
          1135 => 'Noto Sans Yi',
          1136 => 'Noto Sans Zanabazar Square',
          1137 => 'Noto Serif',
          1138 => 'Noto Serif Ahom',
          1139 => 'Noto Serif Armenian',
          1140 => 'Noto Serif Balinese',
          1141 => 'Noto Serif Bengali',
          1142 => 'Noto Serif Devanagari',
          1143 => 'Noto Serif Display',
          1144 => 'Noto Serif Dogra',
          1145 => 'Noto Serif Ethiopic',
          1146 => 'Noto Serif Georgian',
          1147 => 'Noto Serif Grantha',
          1148 => 'Noto Serif Gujarati',
          1149 => 'Noto Serif Gurmukhi',
          1150 => 'Noto Serif HK',
          1151 => 'Noto Serif Hebrew',
          1152 => 'Noto Serif Hentaigana',
          1153 => 'Noto Serif JP',
          1154 => 'Noto Serif KR',
          1155 => 'Noto Serif Kannada',
          1156 => 'Noto Serif Khitan Small Script',
          1157 => 'Noto Serif Khmer',
          1158 => 'Noto Serif Khojki',
          1159 => 'Noto Serif Lao',
          1160 => 'Noto Serif Makasar',
          1161 => 'Noto Serif Malayalam',
          1162 => 'Noto Serif Myanmar',
          1163 => 'Noto Serif NP Hmong',
          1164 => 'Noto Serif Old Uyghur',
          1165 => 'Noto Serif Oriya',
          1166 => 'Noto Serif Ottoman Siyaq',
          1167 => 'Noto Serif SC',
          1168 => 'Noto Serif Sinhala',
          1169 => 'Noto Serif TC',
          1170 => 'Noto Serif Tamil',
          1171 => 'Noto Serif Tangut',
          1172 => 'Noto Serif Telugu',
          1173 => 'Noto Serif Thai',
          1174 => 'Noto Serif Tibetan',
          1175 => 'Noto Serif Todhri',
          1176 => 'Noto Serif Toto',
          1177 => 'Noto Serif Vithkuqi',
          1178 => 'Noto Serif Yezidi',
          1179 => 'Noto Traditional Nushu',
          1180 => 'Noto Znamenny Musical Notation',
          1181 => 'Nova Cut',
          1182 => 'Nova Flat',
          1183 => 'Nova Mono',
          1184 => 'Nova Oval',
          1185 => 'Nova Round',
          1186 => 'Nova Script',
          1187 => 'Nova Slim',
          1188 => 'Nova Square',
          1189 => 'Numans',
          1190 => 'Nunito',
          1191 => 'Nunito Sans',
          1192 => 'Nuosu SIL',
          1193 => 'Odibee Sans',
          1194 => 'Odor Mean Chey',
          1195 => 'Offside',
          1196 => 'Oi',
          1197 => 'Ojuju',
          1198 => 'Old Standard TT',
          1199 => 'Oldenburg',
          1200 => 'Ole',
          1201 => 'Oleo Script',
          1202 => 'Oleo Script Swash Caps',
          1203 => 'Onest',
          1204 => 'Oooh Baby',
          1205 => 'Open Sans',
          1206 => 'Oranienbaum',
          1207 => 'Orbit',
          1208 => 'Orbitron',
          1209 => 'Oregano',
          1210 => 'Orelega One',
          1211 => 'Orienta',
          1212 => 'Original Surfer',
          1213 => 'Oswald',
          1214 => 'Outfit',
          1215 => 'Over the Rainbow',
          1216 => 'Overlock',
          1217 => 'Overlock SC',
          1218 => 'Overpass',
          1219 => 'Overpass Mono',
          1220 => 'Ovo',
          1221 => 'Oxanium',
          1222 => 'Oxygen',
          1223 => 'Oxygen Mono',
          1224 => 'PT Mono',
          1225 => 'PT Sans',
          1226 => 'PT Sans Caption',
          1227 => 'PT Sans Narrow',
          1228 => 'PT Serif',
          1229 => 'PT Serif Caption',
          1230 => 'Pacifico',
          1231 => 'Padauk',
          1232 => 'Padyakke Expanded One',
          1233 => 'Palanquin',
          1234 => 'Palanquin Dark',
          1235 => 'Palette Mosaic',
          1236 => 'Pangolin',
          1237 => 'Paprika',
          1238 => 'Parisienne',
          1239 => 'Parkinsans',
          1240 => 'Passero One',
          1241 => 'Passion One',
          1242 => 'Passions Conflict',
          1243 => 'Pathway Extreme',
          1244 => 'Pathway Gothic One',
          1245 => 'Patrick Hand',
          1246 => 'Patrick Hand SC',
          1247 => 'Pattaya',
          1248 => 'Patua One',
          1249 => 'Pavanam',
          1250 => 'Paytone One',
          1251 => 'Peddana',
          1252 => 'Peralta',
          1253 => 'Permanent Marker',
          1254 => 'Petemoss',
          1255 => 'Petit Formal Script',
          1256 => 'Petrona',
          1257 => 'Phetsarath',
          1258 => 'Philosopher',
          1259 => 'Phudu',
          1260 => 'Piazzolla',
          1261 => 'Piedra',
          1262 => 'Pinyon Script',
          1263 => 'Pirata One',
          1264 => 'Pixelify Sans',
          1265 => 'Plaster',
          1266 => 'Platypi',
          1267 => 'Play',
          1268 => 'Playball',
          1269 => 'Playfair',
          1270 => 'Playfair Display',
          1271 => 'Playfair Display SC',
          1272 => 'Playpen Sans',
          1273 => 'Playwrite AR',
          1274 => 'Playwrite AR Guides',
          1275 => 'Playwrite AT',
          1276 => 'Playwrite AT Guides',
          1277 => 'Playwrite AU NSW',
          1278 => 'Playwrite AU NSW Guides',
          1279 => 'Playwrite AU QLD',
          1280 => 'Playwrite AU QLD Guides',
          1281 => 'Playwrite AU SA',
          1282 => 'Playwrite AU SA Guides',
          1283 => 'Playwrite AU TAS',
          1284 => 'Playwrite AU TAS Guides',
          1285 => 'Playwrite AU VIC',
          1286 => 'Playwrite AU VIC Guides',
          1287 => 'Playwrite BE VLG',
          1288 => 'Playwrite BE VLG Guides',
          1289 => 'Playwrite BE WAL',
          1290 => 'Playwrite BE WAL Guides',
          1291 => 'Playwrite BR',
          1292 => 'Playwrite BR Guides',
          1293 => 'Playwrite CA',
          1294 => 'Playwrite CA Guides',
          1295 => 'Playwrite CL',
          1296 => 'Playwrite CL Guides',
          1297 => 'Playwrite CO',
          1298 => 'Playwrite CO Guides',
          1299 => 'Playwrite CU',
          1300 => 'Playwrite CU Guides',
          1301 => 'Playwrite CZ',
          1302 => 'Playwrite CZ Guides',
          1303 => 'Playwrite DE Grund',
          1304 => 'Playwrite DE Grund Guides',
          1305 => 'Playwrite DE LA',
          1306 => 'Playwrite DE LA Guides',
          1307 => 'Playwrite DE SAS',
          1308 => 'Playwrite DE SAS Guides',
          1309 => 'Playwrite DE VA',
          1310 => 'Playwrite DE VA Guides',
          1311 => 'Playwrite DK Loopet',
          1312 => 'Playwrite DK Loopet Guides',
          1313 => 'Playwrite DK Uloopet',
          1314 => 'Playwrite DK Uloopet Guides',
          1315 => 'Playwrite ES',
          1316 => 'Playwrite ES Deco',
          1317 => 'Playwrite ES Deco Guides',
          1318 => 'Playwrite ES Guides',
          1319 => 'Playwrite FR Moderne',
          1320 => 'Playwrite FR Moderne Guides',
          1321 => 'Playwrite FR Trad',
          1322 => 'Playwrite FR Trad Guides',
          1323 => 'Playwrite GB J',
          1324 => 'Playwrite GB J Guides',
          1325 => 'Playwrite GB S',
          1326 => 'Playwrite GB S Guides',
          1327 => 'Playwrite HR',
          1328 => 'Playwrite HR Guides',
          1329 => 'Playwrite HR Lijeva',
          1330 => 'Playwrite HR Lijeva Guides',
          1331 => 'Playwrite HU',
          1332 => 'Playwrite HU Guides',
          1333 => 'Playwrite ID',
          1334 => 'Playwrite ID Guides',
          1335 => 'Playwrite IE',
          1336 => 'Playwrite IE Guides',
          1337 => 'Playwrite IN',
          1338 => 'Playwrite IN Guides',
          1339 => 'Playwrite IS',
          1340 => 'Playwrite IS Guides',
          1341 => 'Playwrite IT Moderna',
          1342 => 'Playwrite IT Moderna Guides',
          1343 => 'Playwrite IT Trad',
          1344 => 'Playwrite IT Trad Guides',
          1345 => 'Playwrite MX',
          1346 => 'Playwrite MX Guides',
          1347 => 'Playwrite NG Modern',
          1348 => 'Playwrite NG Modern Guides',
          1349 => 'Playwrite NL',
          1350 => 'Playwrite NL Guides',
          1351 => 'Playwrite NO',
          1352 => 'Playwrite NO Guides',
          1353 => 'Playwrite NZ',
          1354 => 'Playwrite NZ Guides',
          1355 => 'Playwrite PE',
          1356 => 'Playwrite PE Guides',
          1357 => 'Playwrite PL',
          1358 => 'Playwrite PL Guides',
          1359 => 'Playwrite PT',
          1360 => 'Playwrite PT Guides',
          1361 => 'Playwrite RO',
          1362 => 'Playwrite RO Guides',
          1363 => 'Playwrite SK',
          1364 => 'Playwrite SK Guides',
          1365 => 'Playwrite TZ',
          1366 => 'Playwrite TZ Guides',
          1367 => 'Playwrite US Modern',
          1368 => 'Playwrite US Modern Guides',
          1369 => 'Playwrite US Trad',
          1370 => 'Playwrite US Trad Guides',
          1371 => 'Playwrite VN',
          1372 => 'Playwrite VN Guides',
          1373 => 'Playwrite ZA',
          1374 => 'Playwrite ZA Guides',
          1375 => 'Plus Jakarta Sans',
          1376 => 'Pochaevsk',
          1377 => 'Podkova',
          1378 => 'Poetsen One',
          1379 => 'Poiret One',
          1380 => 'Poller One',
          1381 => 'Poltawski Nowy',
          1382 => 'Poly',
          1383 => 'Pompiere',
          1384 => 'Ponnala',
          1385 => 'Ponomar',
          1386 => 'Pontano Sans',
          1387 => 'Poor Story',
          1388 => 'Poppins',
          1389 => 'Port Lligat Sans',
          1390 => 'Port Lligat Slab',
          1391 => 'Potta One',
          1392 => 'Pragati Narrow',
          1393 => 'Praise',
          1394 => 'Prata',
          1395 => 'Preahvihear',
          1396 => 'Press Start 2P',
          1397 => 'Pridi',
          1398 => 'Princess Sofia',
          1399 => 'Prociono',
          1400 => 'Prompt',
          1401 => 'Prosto One',
          1402 => 'Protest Guerrilla',
          1403 => 'Protest Revolution',
          1404 => 'Protest Riot',
          1405 => 'Protest Strike',
          1406 => 'Proza Libre',
          1407 => 'Public Sans',
          1408 => 'Puppies Play',
          1409 => 'Puritan',
          1410 => 'Purple Purse',
          1411 => 'Qahiri',
          1412 => 'Quando',
          1413 => 'Quantico',
          1414 => 'Quattrocento',
          1415 => 'Quattrocento Sans',
          1416 => 'Questrial',
          1417 => 'Quicksand',
          1418 => 'Quintessential',
          1419 => 'Qwigley',
          1420 => 'Qwitcher Grypen',
          1421 => 'REM',
          1422 => 'Racing Sans One',
          1423 => 'Radio Canada',
          1424 => 'Radio Canada Big',
          1425 => 'Radley',
          1426 => 'Rajdhani',
          1427 => 'Rakkas',
          1428 => 'Raleway',
          1429 => 'Raleway Dots',
          1430 => 'Ramabhadra',
          1431 => 'Ramaraja',
          1432 => 'Rambla',
          1433 => 'Rammetto One',
          1434 => 'Rampart One',
          1435 => 'Ranchers',
          1436 => 'Rancho',
          1437 => 'Ranga',
          1438 => 'Rasa',
          1439 => 'Rationale',
          1440 => 'Ravi Prakash',
          1441 => 'Readex Pro',
          1442 => 'Recursive',
          1443 => 'Red Hat Display',
          1444 => 'Red Hat Mono',
          1445 => 'Red Hat Text',
          1446 => 'Red Rose',
          1447 => 'Redacted',
          1448 => 'Redacted Script',
          1449 => 'Reddit Mono',
          1450 => 'Reddit Sans',
          1451 => 'Reddit Sans Condensed',
          1452 => 'Redressed',
          1453 => 'Reem Kufi',
          1454 => 'Reem Kufi Fun',
          1455 => 'Reem Kufi Ink',
          1456 => 'Reenie Beanie',
          1457 => 'Reggae One',
          1458 => 'Rethink Sans',
          1459 => 'Revalia',
          1460 => 'Rhodium Libre',
          1461 => 'Ribeye',
          1462 => 'Ribeye Marrow',
          1463 => 'Righteous',
          1464 => 'Risque',
          1465 => 'Road Rage',
          1466 => 'Roboto',
          1467 => 'Roboto Condensed',
          1468 => 'Roboto Flex',
          1469 => 'Roboto Mono',
          1470 => 'Roboto Serif',
          1471 => 'Roboto Slab',
          1472 => 'Rochester',
          1473 => 'Rock 3D',
          1474 => 'Rock Salt',
          1475 => 'RocknRoll One',
          1476 => 'Rokkitt',
          1477 => 'Romanesco',
          1478 => 'Ropa Sans',
          1479 => 'Rosario',
          1480 => 'Rosarivo',
          1481 => 'Rouge Script',
          1482 => 'Rowdies',
          1483 => 'Rozha One',
          1484 => 'Rubik',
          1485 => 'Rubik 80s Fade',
          1486 => 'Rubik Beastly',
          1487 => 'Rubik Broken Fax',
          1488 => 'Rubik Bubbles',
          1489 => 'Rubik Burned',
          1490 => 'Rubik Dirt',
          1491 => 'Rubik Distressed',
          1492 => 'Rubik Doodle Shadow',
          1493 => 'Rubik Doodle Triangles',
          1494 => 'Rubik Gemstones',
          1495 => 'Rubik Glitch',
          1496 => 'Rubik Glitch Pop',
          1497 => 'Rubik Iso',
          1498 => 'Rubik Lines',
          1499 => 'Rubik Maps',
          1500 => 'Rubik Marker Hatch',
          1501 => 'Rubik Maze',
          1502 => 'Rubik Microbe',
          1503 => 'Rubik Mono One',
          1504 => 'Rubik Moonrocks',
          1505 => 'Rubik Pixels',
          1506 => 'Rubik Puddles',
          1507 => 'Rubik Scribble',
          1508 => 'Rubik Spray Paint',
          1509 => 'Rubik Storm',
          1510 => 'Rubik Vinyl',
          1511 => 'Rubik Wet Paint',
          1512 => 'Ruda',
          1513 => 'Rufina',
          1514 => 'Ruge Boogie',
          1515 => 'Ruluko',
          1516 => 'Rum Raisin',
          1517 => 'Ruslan Display',
          1518 => 'Russo One',
          1519 => 'Ruthie',
          1520 => 'Ruwudu',
          1521 => 'Rye',
          1522 => 'STIX Two Text',
          1523 => 'SUSE',
          1524 => 'Sacramento',
          1525 => 'Sahitya',
          1526 => 'Sail',
          1527 => 'Saira',
          1528 => 'Saira Condensed',
          1529 => 'Saira Extra Condensed',
          1530 => 'Saira Semi Condensed',
          1531 => 'Saira Stencil One',
          1532 => 'Salsa',
          1533 => 'Sanchez',
          1534 => 'Sancreek',
          1535 => 'Sankofa Display',
          1536 => 'Sansation',
          1537 => 'Sansita',
          1538 => 'Sansita Swashed',
          1539 => 'Sarabun',
          1540 => 'Sarala',
          1541 => 'Sarina',
          1542 => 'Sarpanch',
          1543 => 'Sassy Frass',
          1544 => 'Satisfy',
          1545 => 'Sawarabi Gothic',
          1546 => 'Sawarabi Mincho',
          1547 => 'Scada',
          1548 => 'Scheherazade New',
          1549 => 'Schibsted Grotesk',
          1550 => 'Schoolbell',
          1551 => 'Scope One',
          1552 => 'Seaweed Script',
          1553 => 'Secular One',
          1554 => 'Sedan',
          1555 => 'Sedan SC',
          1556 => 'Sedgwick Ave',
          1557 => 'Sedgwick Ave Display',
          1558 => 'Sen',
          1559 => 'Send Flowers',
          1560 => 'Sevillana',
          1561 => 'Seymour One',
          1562 => 'Shadows Into Light',
          1563 => 'Shadows Into Light Two',
          1564 => 'Shafarik',
          1565 => 'Shalimar',
          1566 => 'Shantell Sans',
          1567 => 'Shanti',
          1568 => 'Share',
          1569 => 'Share Tech',
          1570 => 'Share Tech Mono',
          1571 => 'Shippori Antique',
          1572 => 'Shippori Antique B1',
          1573 => 'Shippori Mincho',
          1574 => 'Shippori Mincho B1',
          1575 => 'Shizuru',
          1576 => 'Shojumaru',
          1577 => 'Short Stack',
          1578 => 'Shrikhand',
          1579 => 'Siemreap',
          1580 => 'Sigmar',
          1581 => 'Sigmar One',
          1582 => 'Signika',
          1583 => 'Signika Negative',
          1584 => 'Silkscreen',
          1585 => 'Simonetta',
          1586 => 'Single Day',
          1587 => 'Sintony',
          1588 => 'Sirin Stencil',
          1589 => 'Six Caps',
          1590 => 'Sixtyfour',
          1591 => 'Sixtyfour Convergence',
          1592 => 'Skranji',
          1593 => 'Slabo 13px',
          1594 => 'Slabo 27px',
          1595 => 'Slackey',
          1596 => 'Slackside One',
          1597 => 'Smokum',
          1598 => 'Smooch',
          1599 => 'Smooch Sans',
          1600 => 'Smythe',
          1601 => 'Sniglet',
          1602 => 'Snippet',
          1603 => 'Snowburst One',
          1604 => 'Sofadi One',
          1605 => 'Sofia',
          1606 => 'Sofia Sans',
          1607 => 'Sofia Sans Condensed',
          1608 => 'Sofia Sans Extra Condensed',
          1609 => 'Sofia Sans Semi Condensed',
          1610 => 'Solitreo',
          1611 => 'Solway',
          1612 => 'Sometype Mono',
          1613 => 'Song Myung',
          1614 => 'Sono',
          1615 => 'Sonsie One',
          1616 => 'Sora',
          1617 => 'Sorts Mill Goudy',
          1618 => 'Sour Gummy',
          1619 => 'Source Code Pro',
          1620 => 'Source Sans 3',
          1621 => 'Source Serif 4',
          1622 => 'Space Grotesk',
          1623 => 'Space Mono',
          1624 => 'Special Elite',
          1625 => 'Special Gothic',
          1626 => 'Special Gothic Condensed One',
          1627 => 'Special Gothic Expanded One',
          1628 => 'Spectral',
          1629 => 'Spectral SC',
          1630 => 'Spicy Rice',
          1631 => 'Spinnaker',
          1632 => 'Spirax',
          1633 => 'Splash',
          1634 => 'Spline Sans',
          1635 => 'Spline Sans Mono',
          1636 => 'Squada One',
          1637 => 'Square Peg',
          1638 => 'Sree Krushnadevaraya',
          1639 => 'Sriracha',
          1640 => 'Srisakdi',
          1641 => 'Staatliches',
          1642 => 'Stalemate',
          1643 => 'Stalinist One',
          1644 => 'Stardos Stencil',
          1645 => 'Stick',
          1646 => 'Stick No Bills',
          1647 => 'Stint Ultra Condensed',
          1648 => 'Stint Ultra Expanded',
          1649 => 'Stoke',
          1650 => 'Strait',
          1651 => 'Style Script',
          1652 => 'Stylish',
          1653 => 'Sue Ellen Francisco',
          1654 => 'Suez One',
          1655 => 'Sulphur Point',
          1656 => 'Sumana',
          1657 => 'Sunflower',
          1658 => 'Sunshiney',
          1659 => 'Supermercado One',
          1660 => 'Sura',
          1661 => 'Suranna',
          1662 => 'Suravaram',
          1663 => 'Suwannaphum',
          1664 => 'Swanky and Moo Moo',
          1665 => 'Syncopate',
          1666 => 'Syne',
          1667 => 'Syne Mono',
          1668 => 'Syne Tactile',
          1669 => 'Tac One',
          1670 => 'Tagesschrift',
          1671 => 'Tai Heritage Pro',
          1672 => 'Tajawal',
          1673 => 'Tangerine',
          1674 => 'Tapestry',
          1675 => 'Taprom',
          1676 => 'Tauri',
          1677 => 'Taviraj',
          1678 => 'Teachers',
          1679 => 'Teko',
          1680 => 'Tektur',
          1681 => 'Telex',
          1682 => 'Tenali Ramakrishna',
          1683 => 'Tenor Sans',
          1684 => 'Text Me One',
          1685 => 'Texturina',
          1686 => 'Thasadith',
          1687 => 'The Girl Next Door',
          1688 => 'The Nautigal',
          1689 => 'Tienne',
          1690 => 'Tillana',
          1691 => 'Tilt Neon',
          1692 => 'Tilt Prism',
          1693 => 'Tilt Warp',
          1694 => 'Timmana',
          1695 => 'Tinos',
          1696 => 'Tiny5',
          1697 => 'Tiro Bangla',
          1698 => 'Tiro Devanagari Hindi',
          1699 => 'Tiro Devanagari Marathi',
          1700 => 'Tiro Devanagari Sanskrit',
          1701 => 'Tiro Gurmukhi',
          1702 => 'Tiro Kannada',
          1703 => 'Tiro Tamil',
          1704 => 'Tiro Telugu',
          1705 => 'Titan One',
          1706 => 'Titillium Web',
          1707 => 'Tomorrow',
          1708 => 'Tourney',
          1709 => 'Trade Winds',
          1710 => 'Train One',
          1711 => 'Triodion',
          1712 => 'Trirong',
          1713 => 'Trispace',
          1714 => 'Trocchi',
          1715 => 'Trochut',
          1716 => 'Truculenta',
          1717 => 'Trykker',
          1718 => 'Tsukimi Rounded',
          1719 => 'Tuffy',
          1720 => 'Tulpen One',
          1721 => 'Turret Road',
          1722 => 'Twinkle Star',
          1723 => 'Ubuntu',
          1724 => 'Ubuntu Condensed',
          1725 => 'Ubuntu Mono',
          1726 => 'Ubuntu Sans',
          1727 => 'Ubuntu Sans Mono',
          1728 => 'Uchen',
          1729 => 'Ultra',
          1730 => 'Unbounded',
          1731 => 'Uncial Antiqua',
          1732 => 'Underdog',
          1733 => 'Unica One',
          1734 => 'UnifrakturCook',
          1735 => 'UnifrakturMaguntia',
          1736 => 'Unkempt',
          1737 => 'Unlock',
          1738 => 'Unna',
          1739 => 'Updock',
          1740 => 'Urbanist',
          1741 => 'VT323',
          1742 => 'Vampiro One',
          1743 => 'Varela',
          1744 => 'Varela Round',
          1745 => 'Varta',
          1746 => 'Vast Shadow',
          1747 => 'Vazirmatn',
          1748 => 'Vesper Libre',
          1749 => 'Viaoda Libre',
          1750 => 'Vibes',
          1751 => 'Vibur',
          1752 => 'Victor Mono',
          1753 => 'Vidaloka',
          1754 => 'Viga',
          1755 => 'Vina Sans',
          1756 => 'Voces',
          1757 => 'Volkhov',
          1758 => 'Vollkorn',
          1759 => 'Vollkorn SC',
          1760 => 'Voltaire',
          1761 => 'Vujahday Script',
          1762 => 'Waiting for the Sunrise',
          1763 => 'Wallpoet',
          1764 => 'Walter Turncoat',
          1765 => 'Warnes',
          1766 => 'Water Brush',
          1767 => 'Waterfall',
          1768 => 'Wavefont',
          1769 => 'Wellfleet',
          1770 => 'Wendy One',
          1771 => 'Whisper',
          1772 => 'WindSong',
          1773 => 'Winky Rough',
          1774 => 'Winky Sans',
          1775 => 'Wire One',
          1776 => 'Wittgenstein',
          1777 => 'Wix Madefor Display',
          1778 => 'Wix Madefor Text',
          1779 => 'Work Sans',
          1780 => 'Workbench',
          1781 => 'Xanh Mono',
          1782 => 'Yaldevi',
          1783 => 'Yanone Kaffeesatz',
          1784 => 'Yantramanav',
          1785 => 'Yarndings 12',
          1786 => 'Yarndings 12 Charted',
          1787 => 'Yarndings 20',
          1788 => 'Yarndings 20 Charted',
          1789 => 'Yatra One',
          1790 => 'Yellowtail',
          1791 => 'Yeon Sung',
          1792 => 'Yeseva One',
          1793 => 'Yesteryear',
          1794 => 'Yomogi',
          1795 => 'Young Serif',
          1796 => 'Yrsa',
          1797 => 'Ysabeau',
          1798 => 'Ysabeau Infant',
          1799 => 'Ysabeau Office',
          1800 => 'Ysabeau SC',
          1801 => 'Yuji Boku',
          1802 => 'Yuji Hentaigana Akari',
          1803 => 'Yuji Hentaigana Akebono',
          1804 => 'Yuji Mai',
          1805 => 'Yuji Syuku',
          1806 => 'Yusei Magic',
          1807 => 'ZCOOL KuaiLe',
          1808 => 'ZCOOL QingKe HuangYou',
          1809 => 'ZCOOL XiaoWei',
          1810 => 'Zain',
          1811 => 'Zen Antique',
          1812 => 'Zen Antique Soft',
          1813 => 'Zen Dots',
          1814 => 'Zen Kaku Gothic Antique',
          1815 => 'Zen Kaku Gothic New',
          1816 => 'Zen Kurenaido',
          1817 => 'Zen Loop',
          1818 => 'Zen Maru Gothic',
          1819 => 'Zen Old Mincho',
          1820 => 'Zen Tokyo Zoo',
          1821 => 'Zeyada',
          1822 => 'Zhi Mang Xing',
          1823 => 'Zilla Slab',
          1824 => 'Zilla Slab Highlight',
        ),
        'custom_google_fonts' => '',
        'custom_fonts' => '',
        'countries' => 
        array (
          'AF' => 'Afghanistan',
          'AX' => 'Åland Islands',
          'AL' => 'Albania',
          'DZ' => 'Algeria',
          'AS' => 'American Samoa',
          'AD' => 'Andorra',
          'AO' => 'Angola',
          'AI' => 'Anguilla',
          'AQ' => 'Antarctica',
          'AG' => 'Antigua and Barbuda',
          'AR' => 'Argentina',
          'AM' => 'Armenia',
          'AW' => 'Aruba',
          'AU' => 'Australia',
          'AT' => 'Austria',
          'AZ' => 'Azerbaijan',
          'BS' => 'Bahamas',
          'BH' => 'Bahrain',
          'BD' => 'Bangladesh',
          'BB' => 'Barbados',
          'BY' => 'Belarus',
          'BE' => 'Belgium',
          'PW' => 'Belau',
          'BZ' => 'Belize',
          'BJ' => 'Benin',
          'BM' => 'Bermuda',
          'BT' => 'Bhutan',
          'BO' => 'Bolivia',
          'BQ' => 'Bonaire, Saint Eustatius and Saba',
          'BA' => 'Bosnia and Herzegovina',
          'BW' => 'Botswana',
          'BV' => 'Bouvet Island',
          'BR' => 'Brazil',
          'IO' => 'British Indian Ocean Territory',
          'BN' => 'Brunei',
          'BG' => 'Bulgaria',
          'BF' => 'Burkina Faso',
          'BI' => 'Burundi',
          'KH' => 'Cambodia',
          'CM' => 'Cameroon',
          'CA' => 'Canada',
          'CV' => 'Cape Verde',
          'KY' => 'Cayman Islands',
          'CF' => 'Central African Republic',
          'TD' => 'Chad',
          'CL' => 'Chile',
          'CN' => 'China',
          'CX' => 'Christmas Island',
          'CC' => 'Cocos (Keeling) Islands',
          'CO' => 'Colombia',
          'KM' => 'Comoros',
          'CG' => 'Congo (Brazzaville)',
          'CD' => 'Congo (Kinshasa)',
          'CK' => 'Cook Islands',
          'CR' => 'Costa Rica',
          'HR' => 'Croatia',
          'CU' => 'Cuba',
          'CW' => 'Cura&ccedil;ao',
          'CY' => 'Cyprus',
          'CZ' => 'Czech Republic',
          'DK' => 'Denmark',
          'DJ' => 'Djibouti',
          'DM' => 'Dominica',
          'DO' => 'Dominican Republic',
          'EC' => 'Ecuador',
          'EG' => 'Egypt',
          'SV' => 'El Salvador',
          'GQ' => 'Equatorial Guinea',
          'ER' => 'Eritrea',
          'EE' => 'Estonia',
          'ET' => 'Ethiopia',
          'FK' => 'Falkland Islands',
          'FO' => 'Faroe Islands',
          'FJ' => 'Fiji',
          'FI' => 'Finland',
          'FR' => 'France',
          'GF' => 'French Guiana',
          'PF' => 'French Polynesia',
          'TF' => 'French Southern Territories',
          'GA' => 'Gabon',
          'GM' => 'Gambia',
          'GE' => 'Georgia',
          'DE' => 'Germany',
          'GH' => 'Ghana',
          'GI' => 'Gibraltar',
          'GR' => 'Greece',
          'GL' => 'Greenland',
          'GD' => 'Grenada',
          'GP' => 'Guadeloupe',
          'GU' => 'Guam',
          'GT' => 'Guatemala',
          'GG' => 'Guernsey',
          'GN' => 'Guinea',
          'GW' => 'Guinea-Bissau',
          'GY' => 'Guyana',
          'HT' => 'Haiti',
          'HM' => 'Heard Island and McDonald Islands',
          'HN' => 'Honduras',
          'HK' => 'Hong Kong',
          'HU' => 'Hungary',
          'IS' => 'Iceland',
          'IN' => 'India',
          'ID' => 'Indonesia',
          'IR' => 'Iran',
          'IQ' => 'Iraq',
          'IE' => 'Ireland',
          'IM' => 'Isle of Man',
          'IL' => 'Israel',
          'IT' => 'Italy',
          'CI' => 'Ivory Coast',
          'JM' => 'Jamaica',
          'JP' => 'Japan',
          'JE' => 'Jersey',
          'JO' => 'Jordan',
          'KZ' => 'Kazakhstan',
          'KE' => 'Kenya',
          'KI' => 'Kiribati',
          'KW' => 'Kuwait',
          'XK' => 'Kosovo',
          'KG' => 'Kyrgyzstan',
          'LA' => 'Laos',
          'LV' => 'Latvia',
          'LB' => 'Lebanon',
          'LS' => 'Lesotho',
          'LR' => 'Liberia',
          'LY' => 'Libya',
          'LI' => 'Liechtenstein',
          'LT' => 'Lithuania',
          'LU' => 'Luxembourg',
          'MO' => 'Macao',
          'MK' => 'North Macedonia',
          'MG' => 'Madagascar',
          'MW' => 'Malawi',
          'MY' => 'Malaysia',
          'MV' => 'Maldives',
          'ML' => 'Mali',
          'MT' => 'Malta',
          'MH' => 'Marshall Islands',
          'MQ' => 'Martinique',
          'MR' => 'Mauritania',
          'MU' => 'Mauritius',
          'YT' => 'Mayotte',
          'MX' => 'Mexico',
          'FM' => 'Micronesia',
          'MD' => 'Moldova',
          'MC' => 'Monaco',
          'MN' => 'Mongolia',
          'ME' => 'Montenegro',
          'MS' => 'Montserrat',
          'MA' => 'Morocco',
          'MZ' => 'Mozambique',
          'MM' => 'Myanmar',
          'NA' => 'Namibia',
          'NR' => 'Nauru',
          'NP' => 'Nepal',
          'NL' => 'Netherlands',
          'NC' => 'New Caledonia',
          'NZ' => 'New Zealand',
          'NI' => 'Nicaragua',
          'NE' => 'Niger',
          'NG' => 'Nigeria',
          'NU' => 'Niue',
          'NF' => 'Norfolk Island',
          'MP' => 'Northern Mariana Islands',
          'KP' => 'North Korea',
          'NO' => 'Norway',
          'OM' => 'Oman',
          'PK' => 'Pakistan',
          'PS' => 'Palestinian Territory',
          'PA' => 'Panama',
          'PG' => 'Papua New Guinea',
          'PY' => 'Paraguay',
          'PE' => 'Peru',
          'PH' => 'Philippines',
          'PN' => 'Pitcairn',
          'PL' => 'Poland',
          'PT' => 'Portugal',
          'PR' => 'Puerto Rico',
          'QA' => 'Qatar',
          'RE' => 'Reunion',
          'RO' => 'Romania',
          'RU' => 'Russia',
          'RW' => 'Rwanda',
          'BL' => 'Saint Barth&eacute;lemy',
          'SH' => 'Saint Helena',
          'KN' => 'Saint Kitts and Nevis',
          'LC' => 'Saint Lucia',
          'MF' => 'Saint Martin (French part)',
          'SX' => 'Saint Martin (Dutch part)',
          'PM' => 'Saint Pierre and Miquelon',
          'VC' => 'Saint Vincent and the Grenadines',
          'SM' => 'San Marino',
          'ST' => 'S&atilde;o Tom&eacute; and Pr&iacute;ncipe',
          'SA' => 'Saudi Arabia',
          'SN' => 'Senegal',
          'RS' => 'Serbia',
          'SC' => 'Seychelles',
          'SL' => 'Sierra Leone',
          'SG' => 'Singapore',
          'SK' => 'Slovakia',
          'SI' => 'Slovenia',
          'SB' => 'Solomon Islands',
          'SO' => 'Somalia',
          'ZA' => 'South Africa',
          'GS' => 'South Georgia/Sandwich Islands',
          'KR' => 'South Korea',
          'SS' => 'South Sudan',
          'ES' => 'Spain',
          'LK' => 'Sri Lanka',
          'SD' => 'Sudan',
          'SR' => 'Suriname',
          'SJ' => 'Svalbard and Jan Mayen',
          'SZ' => 'Swaziland',
          'SE' => 'Sweden',
          'CH' => 'Switzerland',
          'SY' => 'Syria',
          'TW' => 'Taiwan',
          'TJ' => 'Tajikistan',
          'TZ' => 'Tanzania',
          'TH' => 'Thailand',
          'TL' => 'Timor-Leste',
          'TG' => 'Togo',
          'TK' => 'Tokelau',
          'TO' => 'Tonga',
          'TT' => 'Trinidad and Tobago',
          'TN' => 'Tunisia',
          'TR' => 'Turkey',
          'TM' => 'Turkmenistan',
          'TC' => 'Turks and Caicos Islands',
          'TV' => 'Tuvalu',
          'UG' => 'Uganda',
          'UA' => 'Ukraine',
          'AE' => 'United Arab Emirates',
          'GB' => 'United Kingdom (UK)',
          'US' => 'United States (US)',
          'UM' => 'United States (US) Minor Outlying Islands',
          'UY' => 'Uruguay',
          'UZ' => 'Uzbekistan',
          'VU' => 'Vanuatu',
          'VA' => 'Vatican',
          'VE' => 'Venezuela',
          'VN' => 'Vietnam',
          'VG' => 'Virgin Islands (British)',
          'VI' => 'Virgin Islands (US)',
          'WF' => 'Wallis and Futuna',
          'EH' => 'Western Sahara',
          'WS' => 'Samoa',
          'YE' => 'Yemen',
          'ZM' => 'Zambia',
          'ZW' => 'Zimbabwe',
        ),
        'purifier' => 
        array (
          'default' => 
          array (
            'HTML.Doctype' => 'HTML 4.01 Transitional',
            'HTML.Allowed' => 'div,b,strong,i,em,u,a[href|title|rel|style|target|dofollow|nofollow],ul,ol,li,p[style],br,span[style],img[width|height|alt|src|style|loading],button,ins[style|data-ad-client|data-ad-slot|data-ad-format|data-full-width-responsive],video[src|type|width|height|preload|controls|autoplay|autostart|poster|id|class,muted,loop],meta[name|content|property],link[media|type|rel|href]',
            'HTML.AllowedElements' => 
            array (
              0 => 'a',
              1 => 'b',
              2 => 'blockquote',
              3 => 'br',
              4 => 'code',
              5 => 'em',
              6 => 'h1',
              7 => 'h2',
              8 => 'h3',
              9 => 'h4',
              10 => 'h5',
              11 => 'h6',
              12 => 'hr',
              13 => 'i',
              14 => 'img',
              15 => 'li',
              16 => 'ol',
              17 => 'p',
              18 => 'pre',
              19 => 's',
              20 => 'span',
              21 => 'strong',
              22 => 'sub',
              23 => 'sup',
              24 => 'table',
              25 => 'tbody',
              26 => 'td',
              27 => 'dl',
              28 => 'dt',
              29 => 'dd',
              30 => 'th',
              31 => 'thead',
              32 => 'tr',
              33 => 'u',
              34 => 'ul',
              35 => 'pre',
              36 => 'abbr',
              37 => 'kbd',
              38 => 'var',
              39 => 'samp',
              40 => 'hr',
              41 => 'iframe',
              42 => 'figure',
              43 => 'figcaption',
              44 => 'section',
              45 => 'article',
              46 => 'aside',
              47 => 'blockquote',
              48 => 'caption',
              49 => 'del',
              50 => 'div',
              51 => 'button',
              52 => 'ins',
              53 => 'video',
              54 => 'source',
              55 => 'meta',
              56 => 'link',
              57 => 'audio',
            ),
            'HTML.SafeIframe' => 'true',
            'Attr.AllowedFrameTargets' => 
            array (
              0 => '_blank',
            ),
            'CSS.AllowedProperties' => 
            array (
              0 => 'font',
              1 => 'font-size',
              2 => 'font-weight',
              3 => 'font-style',
              4 => 'font-family',
              5 => 'text-decoration',
              6 => 'padding-left',
              7 => 'color',
              8 => 'background-color',
              9 => 'text-align',
              10 => 'max-width',
              11 => 'border',
              12 => 'width',
              13 => 'line-height',
              14 => 'word-spacing',
              15 => 'border-style',
              16 => 'list-style-type',
              17 => 'border-color',
              18 => 'height',
              19 => 'min-width',
              20 => 'min-height',
              21 => 'max-height',
              22 => 'list-style',
              23 => 'margin',
              24 => 'margin-bottom',
              25 => 'margin-left',
              26 => 'margin-right',
              27 => 'margin-top',
              28 => 'padding',
              29 => 'height',
              30 => 'line-height',
              31 => 'border-collapse',
            ),
            'CSS.MaxImgLength' => NULL,
            'AutoFormat.AutoParagraph' => false,
            'AutoFormat.RemoveEmpty' => false,
            'Attr.EnableID' => true,
          ),
          'custom_elements' => 
          array (
            0 => 
            array (
              0 => 'u',
              1 => 'Inline',
              2 => 'Inline',
              3 => 'Common',
            ),
            1 => 
            array (
              0 => 'button',
              1 => 'Inline',
              2 => 'Inline',
              3 => 'Common',
            ),
            2 => 
            array (
              0 => 'ins',
              1 => 'Inline',
              2 => 'Inline',
              3 => 'Common',
            ),
            3 => 
            array (
              0 => 'meta',
              1 => 'Inline',
              2 => 'Empty',
              3 => 'Common',
            ),
            4 => 
            array (
              0 => 'link',
              1 => 'Inline',
              2 => 'Empty',
              3 => 'Common',
            ),
            5 => 
            array (
              0 => 'audio',
              1 => 'Block',
              2 => 'Optional: (source, Flow) | (Flow, source) | Flow',
              3 => 'Common',
            ),
          ),
          'custom_attributes' => 
          array (
            0 => 
            array (
              0 => 'a',
              1 => 'rel',
              2 => 'Text',
            ),
            1 => 
            array (
              0 => 'a',
              1 => 'dofollow',
              2 => 'Bool',
            ),
            2 => 
            array (
              0 => 'a',
              1 => 'nofollow',
              2 => 'Bool',
            ),
            3 => 
            array (
              0 => 'span',
              1 => 'data-period',
              2 => 'Text',
            ),
            4 => 
            array (
              0 => 'span',
              1 => 'data-type',
              2 => 'Text',
            ),
            5 => 
            array (
              0 => 'ins',
              1 => 'data-ad-client',
              2 => 'Text',
            ),
            6 => 
            array (
              0 => 'ins',
              1 => 'data-ad-slot',
              2 => 'Text',
            ),
            7 => 
            array (
              0 => 'ins',
              1 => 'data-ad-format',
              2 => 'Text',
            ),
            8 => 
            array (
              0 => 'ins',
              1 => 'data-ad-full-width-responsive',
              2 => 'Text',
            ),
            9 => 
            array (
              0 => 'img',
              1 => 'data-src',
              2 => 'Text',
            ),
            10 => 
            array (
              0 => 'img',
              1 => 'loading',
              2 => 'Text',
            ),
            11 => 
            array (
              0 => 'video',
              1 => 'autoplay',
              2 => 'Bool',
            ),
            12 => 
            array (
              0 => 'video',
              1 => 'muted',
              2 => 'Bool',
            ),
            13 => 
            array (
              0 => 'video',
              1 => 'loop',
              2 => 'Bool',
            ),
            14 => 
            array (
              0 => 'meta',
              1 => 'name',
              2 => 'Text',
            ),
            15 => 
            array (
              0 => 'meta',
              1 => 'content',
              2 => 'Text',
            ),
            16 => 
            array (
              0 => 'meta',
              1 => 'property',
              2 => 'Text',
            ),
            17 => 
            array (
              0 => 'link',
              1 => 'media',
              2 => 'Text',
            ),
            18 => 
            array (
              0 => 'link',
              1 => 'type',
              2 => 'Text',
            ),
            19 => 
            array (
              0 => 'link',
              1 => 'rel',
              2 => 'Text',
            ),
            20 => 
            array (
              0 => 'link',
              1 => 'href',
              2 => 'Text',
            ),
            21 => 
            array (
              0 => 'link',
              1 => 'color',
              2 => 'Text',
            ),
            22 => 
            array (
              0 => 'audio',
              1 => 'controls',
              2 => 'Bool',
            ),
            23 => 
            array (
              0 => 'div',
              1 => 'data-bs-theme',
              2 => 'Text',
            ),
            24 => 
            array (
              0 => 'div',
              1 => 'data-url',
              2 => 'Text',
            ),
            25 => 
            array (
              0 => 'button',
              1 => 'data-bb-toggle',
              2 => 'Text',
            ),
            26 => 
            array (
              0 => 'button',
              1 => 'data-value',
              2 => 'Text',
            ),
          ),
        ),
        'enable_system_updater' => true,
        'phone_validation_rule' => 'min:8|max:15|regex:/^([0-9\\s\\-\\+\\(\\)]*)$/',
        'zipcode_validation_rule' => 'string|min:4|max:9',
        'disable_verify_csrf_token' => false,
        'enable_less_secure_web' => false,
        'db_strict_mode' => false,
        'db_prefix' => '',
        'enable_ini_set' => true,
        'upgrade_php_require_disabled' => false,
        'enabled_cleanup_database' => false,
        'hide_cleanup_system_menu' => false,
        'hide_activated_license_info' => false,
        'google_fonts_url' => 'https://fonts.bunny.net',
        'google_fonts_enabled' => true,
        'google_fonts_enabled_cache' => true,
        'using_uuids_for_id' => false,
        'using_ulids_for_id' => false,
        'type_id' => 'BIGINT',
        'csv_import_input_encoding' => 'UTF-8',
        'google_fonts_key' => NULL,
        'demo_mode_enabled' => false,
        'enable_email_configuration_from_admin_panel' => true,
        'session_cookie' => 'botble_session',
        'allowed_iframe_urls' => '',
        'iframe_regex' => '',
        'plugin_namespaces' => 
        array (
          'base' => 'core/base',
          'setting' => 'core/setting',
          'icon' => 'core/icon',
          '' => 
          array (
            '' => 
            array (
              '' => 'packages/data-synchronize',
            ),
          ),
          'get-started' => 'packages/get-started',
          'installer' => 'packages/installer',
          'menu' => 'packages/menu',
          'optimize' => 'packages/optimize',
          'page' => 'packages/page',
          'table' => 'core/table',
          'acl' => 'core/acl',
          'dashboard' => 'core/dashboard',
          'media' => 'core/media',
          'js-validation' => 'core/js-validation',
          'chart' => 'core/chart',
          'plugin-management' => 'packages/plugin-management',
          'ecommerce' => 'plugins/ecommerce',
          'payment' => 'plugins/payment',
          'shippo' => 'plugins/shippo',
          'revision' => 'packages/revision',
          'seo-helper' => 'packages/seo-helper',
          'shortcode' => 'packages/shortcode',
          'sitemap' => 'packages/sitemap',
          'slug' => 'packages/slug',
          'theme' => 'packages/theme',
          'widget' => 'packages/widget',
          'language' => 'plugins/language',
          'language-advanced' => 'plugins/language-advanced',
          'ads' => 'plugins/ads',
          'analytics' => 'plugins/analytics',
          'announcement' => 'plugins/announcement',
          'audit-log' => 'plugins/audit-log',
          'backup' => 'plugins/backup',
          'blog' => 'plugins/blog',
          'captcha' => 'plugins/captcha',
          'contact' => 'plugins/contact',
          'cookie-consent' => 'plugins/cookie-consent',
          'faq' => 'plugins/faq',
          'gallery' => 'plugins/gallery',
          'location' => 'plugins/location',
          'mollie' => 'plugins/mollie',
          'newsletter' => 'plugins/newsletter',
          'paypal' => 'plugins/paypal',
          'paystack' => 'plugins/paystack',
          'razorpay' => 'plugins/razorpay',
          'sale-popup' => 'plugins/sale-popup',
          'simple-slider' => 'plugins/simple-slider',
          'social-login' => 'plugins/social-login',
          'sslcommerz' => 'plugins/sslcommerz',
          'stripe' => 'plugins/stripe',
          'team' => 'plugins/team',
          'testimonial' => 'plugins/testimonial',
          'translation' => 'plugins/translation',
        ),
      ),
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'System',
          'flag' => 'core.system',
        ),
        1 => 
        array (
          'name' => 'CMS',
          'flag' => 'core.cms',
        ),
        2 => 
        array (
          'name' => 'Manage license',
          'flag' => 'core.manage.license',
          'parent_flag' => 'core.system',
        ),
        3 => 
        array (
          'name' => 'Cronjob',
          'flag' => 'systems.cronjob',
          'parent_flag' => 'core.system',
        ),
        4 => 
        array (
          'name' => 'Tools',
          'flag' => 'core.tools',
        ),
        5 => 
        array (
          'name' => 'Import/Export Data',
          'flag' => 'tools.data-synchronize',
          'parent_flag' => 'core.tools',
        ),
      ),
      'assets' => 
      array (
        'offline' => true,
        'enable_version' => true,
        'version' => NULL,
        'scripts' => 
        array (
          0 => 'core-ui',
          1 => 'excanvas',
          2 => 'ie8-fix',
          3 => 'modernizr',
          4 => 'select2',
          5 => 'datepicker',
          6 => 'cookie',
          7 => 'core',
          8 => 'app',
          9 => 'toastr',
          10 => 'custom-scrollbar',
          11 => 'stickytableheaders',
          12 => 'jquery-waypoints',
          13 => 'spectrum',
          14 => 'fancybox',
          15 => 'fslightbox',
        ),
        'styles' => 
        array (
          0 => 'fontawesome',
          1 => 'select2',
          2 => 'toastr',
          3 => 'custom-scrollbar',
          4 => 'datepicker',
          5 => 'spectrum',
          6 => 'fancybox',
        ),
        'resources' => 
        array (
          'scripts' => 
          array (
            'core-ui' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/js/core-ui.js',
              ),
            ),
            'core' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/js/core.js',
              ),
            ),
            'app' => 
            array (
              'use_cdn' => false,
              'location' => 'header',
              'src' => 
              array (
                'local' => 
                array (
                  0 => '/vendor/core/core/base/libraries/jquery.min.js',
                  1 => '/vendor/core/core/base/js/app.js',
                ),
              ),
            ),
            'vue' => 
            array (
              'use_cdn' => false,
              'location' => 'header',
              'src' => 
              array (
                'local' => 
                array (
                  0 => '/vendor/core/core/base/libraries/vue.global.min.js',
                ),
              ),
            ),
            'vue-app' => 
            array (
              'use_cdn' => false,
              'location' => 'header',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/js/vue-app.js',
              ),
            ),
            'bootstrap' => 
            array (
              'use_cdn' => false,
              'location' => 'header',
              'src' => 
              array (
                'local' => 
                array (
                  0 => '/vendor/core/core/base/libraries/bootstrap.bundle.min.js',
                ),
              ),
            ),
            'modernizr' => 
            array (
              'use_cdn' => true,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/modernizr/modernizr.min.js',
                'cdn' => '//cdnjs.cloudflare.com/ajax/libs/modernizr/2.8.3/modernizr.js',
              ),
            ),
            'excanvas' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/excanvas.min.js',
              ),
            ),
            'ie8-fix' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/ie8.fix.min.js',
              ),
            ),
            'counterup' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => 
                array (
                  0 => '/vendor/core/core/base/libraries/counterup/jquery.counterup.min.js',
                ),
              ),
            ),
            'blockui' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/jquery.blockui.min.js',
              ),
            ),
            'jquery-ui' => 
            array (
              'use_cdn' => true,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/jquery-ui/jquery-ui.min.js',
                'cdn' => '//cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.min.js',
              ),
            ),
            'cookie' => 
            array (
              'use_cdn' => true,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/jquery-cookie/jquery.cookie.js',
                'cdn' => '//cdnjs.cloudflare.com/ajax/libs/jquery-cookie/1.4.1/jquery.cookie.min.js',
              ),
            ),
            'dropzone' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/dropzone/dropzone.js',
              ),
            ),
            'jqueryTree' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'include_style' => true,
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/jquery-tree/jquery.tree.min.js',
              ),
            ),
            'jqueryTreeView' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'include_style' => true,
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/jquery-treeview/jquery.treeview.min.js',
              ),
            ),
            'bootstrap-editable' => 
            array (
              'use_cdn' => true,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/bootstrap3-editable/js/bootstrap-editable.min.js',
                'cdn' => '//cdnjs.cloudflare.com/ajax/libs/x-editable/1.5.1/bootstrap3-editable/js/bootstrap-editable.min.js',
              ),
            ),
            'toastr' => 
            array (
              'use_cdn' => true,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/toastr/toastr.min.js',
                'cdn' => '//cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.2/toastr.min.js',
              ),
            ),
            'fancybox' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/fancybox/jquery.fancybox.min.js',
                'cdn' => '//fastly.jsdelivr.net/gh/fancyapps/fancybox@3.5.7/dist/jquery.fancybox.min.js',
              ),
            ),
            'fslightbox' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/fslightbox.js',
                'cdn' => '//fastly.jsdelivr.net/npm/fslightbox@3.4.1/index.min.js',
              ),
            ),
            'datatables' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => 
                array (
                  0 => '/vendor/core/core/base/libraries/datatables/media/js/jquery.dataTables.min.js',
                  1 => '/vendor/core/core/base/libraries/datatables/media/js/dataTables.bootstrap.min.js',
                  2 => '/vendor/core/core/base/libraries/datatables/extensions/Buttons/js/dataTables.buttons.min.js',
                  3 => '/vendor/core/core/base/libraries/datatables/extensions/Buttons/js/buttons.bootstrap.min.js',
                  4 => '/vendor/core/core/base/libraries/datatables/extensions/Responsive/js/dataTables.responsive.min.js',
                ),
              ),
            ),
            'raphael' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => 
                array (
                  0 => '/vendor/core/core/base/libraries/raphael-min.js',
                ),
              ),
            ),
            'morris' => 
            array (
              'use_cdn' => true,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/morris/morris.min.js',
                'cdn' => '//cdnjs.cloudflare.com/ajax/libs/morris.js/0.5.1/morris.min.js',
              ),
            ),
            'select2' => 
            array (
              'use_cdn' => true,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/select2/js/select2.min.js',
                'cdn' => '//cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/js/select2.min.js',
              ),
            ),
            'cropper' => 
            array (
              'use_cdn' => true,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/cropper/cropper.min.js',
                'cdn' => '//cdnjs.cloudflare.com/ajax/libs/cropperjs/1.6.1/cropper.min.js',
              ),
            ),
            'datepicker' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/flatpickr/flatpickr.min.js',
                'cdn' => '//fastly.jsdelivr.net/npm/flatpickr',
              ),
            ),
            'sortable' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/sortable/sortable.min.js',
              ),
            ),
            'jquery-nestable' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/jquery-nestable/jquery.nestable.min.js',
              ),
            ),
            'custom-scrollbar' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/mcustom-scrollbar/jquery.mCustomScrollbar.js',
              ),
            ),
            'stickytableheaders' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/stickytableheaders/jquery.stickytableheaders.js',
              ),
            ),
            'are-you-sure' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/jquery.are-you-sure/jquery.are-you-sure.js',
              ),
            ),
            'moment' => 
            array (
              'use_cdn' => true,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/moment-with-locales.min.js',
                'cdn' => '//cdnjs.cloudflare.com/ajax/libs/moment.js/2.11.1/moment-with-locales.min.js',
              ),
            ),
            'datetimepicker' => 
            array (
              'use_cdn' => true,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/bootstrap-datetimepicker/bootstrap-datetimepicker.min.js',
                'cdn' => '//cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/js/bootstrap-datetimepicker.min.js',
              ),
            ),
            'jquery-waypoints' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/jquery-waypoints/jquery.waypoints.min.js',
              ),
            ),
            'colorpicker' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/bootstrap-colorpicker/js/bootstrap-colorpicker.min.js',
              ),
            ),
            'timepicker' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/bootstrap-timepicker/js/bootstrap-timepicker.min.js',
              ),
            ),
            'spectrum' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/spectrum/spectrum.js',
              ),
            ),
            'input-mask' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/jquery-inputmask/jquery.inputmask.bundle.min.js',
              ),
            ),
            'form-validation' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/js-validation/js/js-validation.js',
              ),
            ),
            'apexchart' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/apexchart/apexcharts.min.js',
              ),
            ),
            'coloris' => 
            array (
              'use_cdn' => true,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/coloris/coloris.min.js',
                'cdn' => '//fastly.jsdelivr.net/gh/mdbassit/Coloris@latest/dist/coloris.min.js',
              ),
            ),
            'tagify' => 
            array (
              'use_cdn' => true,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/tagify/tagify.js',
                'cdn' => '//cdnjs.cloudflare.com/ajax/libs/tagify/4.16.4/tagify.min.js',
              ),
            ),
          ),
          'styles' => 
          array (
            'core' => 
            array (
              'use_cdn' => false,
              'location' => 'header',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/css/core.css',
              ),
            ),
            'fontawesome' => 
            array (
              'use_cdn' => true,
              'location' => 'header',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/font-awesome/css/fontawesome.min.css',
                'cdn' => '//use.fontawesome.com/releases/v6.1.1/css/all.css',
              ),
            ),
            'dropzone' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/dropzone/dropzone.css',
              ),
            ),
            'jqueryTree' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/jquery-tree/jquery.tree.min.css',
              ),
            ),
            'jqueryTreeView' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/jquery-treeview/jquery.treeview.min.css',
              ),
            ),
            'jquery-ui' => 
            array (
              'use_cdn' => true,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/jquery-ui/jquery-ui.min.css',
                'cdn' => '//cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.theme.css',
              ),
            ),
            'toastr' => 
            array (
              'use_cdn' => true,
              'location' => 'header',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/toastr/toastr.min.css',
                'cdn' => '//cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.2/toastr.min.css',
              ),
            ),
            'kendo' => 
            array (
              'use_cdn' => false,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/kendo/kendo.min.css',
              ),
            ),
            'datatables' => 
            array (
              'use_cdn' => false,
              'location' => 'header',
              'src' => 
              array (
                'local' => 
                array (
                  0 => '/vendor/core/core/base/libraries/datatables/media/css/dataTables.bootstrap.min.css',
                  1 => '/vendor/core/core/base/libraries/datatables/extensions/Buttons/css/buttons.bootstrap.min.css',
                  2 => '/vendor/core/core/base/libraries/datatables/extensions/Responsive/css/responsive.bootstrap.min.css',
                ),
              ),
            ),
            'bootstrap-editable' => 
            array (
              'use_cdn' => true,
              'location' => 'footer',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/bootstrap3-editable/css/bootstrap-editable.css',
                'cdn' => '//cdnjs.cloudflare.com/ajax/libs/x-editable/1.5.1/bootstrap3-editable/css/bootstrap-editable.css',
              ),
            ),
            'morris' => 
            array (
              'use_cdn' => true,
              'location' => 'header',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/morris/morris.css',
                'cdn' => '//cdnjs.cloudflare.com/ajax/libs/morris.js/0.5.1/morris.css',
              ),
            ),
            'cropper' => 
            array (
              'use_cdn' => false,
              'location' => 'header',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/cropper/cropper.min.css',
                'cdn' => '//cdnjs.cloudflare.com/ajax/libs/cropperjs/1.6.1/cropper.min.css',
              ),
            ),
            'datepicker' => 
            array (
              'use_cdn' => false,
              'location' => 'header',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/flatpickr/flatpickr.min.css',
                'cdn' => '//fastly.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css',
              ),
            ),
            'jquery-nestable' => 
            array (
              'use_cdn' => false,
              'location' => 'header',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/jquery-nestable/jquery.nestable.min.css',
              ),
            ),
            'select2' => 
            array (
              'use_cdn' => true,
              'location' => 'header',
              'src' => 
              array (
                'local' => 
                array (
                  0 => '/vendor/core/core/base/libraries/select2/css/select2.min.css',
                  1 => '/vendor/core/core/base/css/libraries/select2.css',
                ),
                'cdn' => '//cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/css/select2.min.css',
              ),
            ),
            'fancybox' => 
            array (
              'use_cdn' => false,
              'location' => 'header',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/fancybox/jquery.fancybox.min.css',
                'cdn' => '//fastly.jsdelivr.net/gh/fancyapps/fancybox@3.5.7/dist/jquery.fancybox.min.css',
              ),
            ),
            'custom-scrollbar' => 
            array (
              'use_cdn' => false,
              'location' => 'header',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/mcustom-scrollbar/jquery.mCustomScrollbar.css',
              ),
            ),
            'datetimepicker' => 
            array (
              'use_cdn' => true,
              'location' => 'header',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/bootstrap-datetimepicker/bootstrap-datetimepicker.min.css',
                'cdn' => '//cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/css/bootstrap-datetimepicker.min.css',
              ),
            ),
            'colorpicker' => 
            array (
              'use_cdn' => false,
              'location' => 'header',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/bootstrap-colorpicker/css/bootstrap-colorpicker.min.css',
              ),
            ),
            'timepicker' => 
            array (
              'use_cdn' => false,
              'location' => 'header',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/bootstrap-timepicker/css/bootstrap-timepicker.min.css',
              ),
            ),
            'spectrum' => 
            array (
              'use_cdn' => false,
              'location' => 'header',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/spectrum/spectrum.css',
              ),
            ),
            'apexchart' => 
            array (
              'use_cdn' => false,
              'location' => 'header',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/apexchart/apexcharts.css',
              ),
            ),
            'coloris' => 
            array (
              'use_cdn' => true,
              'location' => 'header',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/coloris/coloris.min.css',
                'cdn' => '//fastly.jsdelivr.net/gh/mdbassit/Coloris@latest/dist/coloris.min.css',
              ),
            ),
            'tagify' => 
            array (
              'use_cdn' => true,
              'location' => 'header',
              'src' => 
              array (
                'local' => '/vendor/core/core/base/libraries/tagify/tagify.css',
                'cdn' => '//cdnjs.cloudflare.com/ajax/libs/tagify/4.16.4/tagify.css',
              ),
            ),
          ),
        ),
      ),
    ),
    'setting' => 
    array (
      'general' => 
      array (
        'driver' => 'database',
        'enable_email_smtp_settings' => true,
      ),
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Settings',
          'flag' => 'settings.index',
        ),
        1 => 
        array (
          'name' => 'Common',
          'flag' => 'settings.common',
          'parent_flag' => 'settings.index',
        ),
        2 => 
        array (
          'name' => 'General',
          'flag' => 'settings.options',
          'parent_flag' => 'settings.common',
        ),
        3 => 
        array (
          'name' => 'Email',
          'flag' => 'settings.email',
          'parent_flag' => 'settings.common',
        ),
        4 => 
        array (
          'name' => 'Media',
          'flag' => 'settings.media',
          'parent_flag' => 'settings.common',
        ),
        5 => 
        array (
          'name' => 'Admin Appearance',
          'flag' => 'settings.admin-appearance',
          'parent_flag' => 'settings.common',
        ),
        6 => 
        array (
          'name' => 'Cache',
          'flag' => 'settings.cache',
          'parent_flag' => 'settings.common',
        ),
        7 => 
        array (
          'name' => 'Datatables',
          'flag' => 'settings.datatables',
          'parent_flag' => 'settings.common',
        ),
        8 => 
        array (
          'name' => 'Email Rules',
          'flag' => 'settings.email.rules',
          'parent_flag' => 'settings.common',
        ),
        9 => 
        array (
          'name' => 'Others',
          'flag' => 'settings.others',
          'parent_flag' => 'settings.index',
        ),
      ),
      'email' => 
      array (
        'name' => 'core/setting::setting.email.base_template',
        'description' => 'core/setting::setting.email.base_template_description',
        'templates' => 
        array (
          'header' => 
          array (
            'title' => 'core/setting::setting.email.template_header',
            'description' => 'core/setting::setting.email.template_header_description',
          ),
          'footer' => 
          array (
            'title' => 'core/setting::setting.email.template_footer',
            'description' => 'core/setting::setting.email.template_footer_description',
          ),
        ),
      ),
    ),
    'icon' => 
    array (
      'icon' => 
      array (
        'className' => 'icon',
        'attributes' => 
        array (
        ),
      ),
    ),
    'acl' => 
    array (
      'general' => 
      array (
        'activations' => 
        array (
          'expires' => 259200,
          'lottery' => 
          array (
            0 => 2,
            1 => 100,
          ),
        ),
        'backgrounds' => 
        array (
          0 => 'vendor/core/core/acl/images/backgrounds/1.jpg',
          1 => 'vendor/core/core/acl/images/backgrounds/2.jpg',
          2 => 'vendor/core/core/acl/images/backgrounds/3.jpg',
          3 => 'vendor/core/core/acl/images/backgrounds/4.jpg',
          4 => 'vendor/core/core/acl/images/backgrounds/5.jpg',
          5 => 'vendor/core/core/acl/images/backgrounds/6.jpg',
          6 => 'vendor/core/core/acl/images/backgrounds/7.jpg',
          7 => 'vendor/core/core/acl/images/backgrounds/8.jpg',
          8 => 'vendor/core/core/acl/images/backgrounds/9.jpg',
          9 => 'vendor/core/core/acl/images/backgrounds/10.jpg',
        ),
      ),
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Users',
          'flag' => 'users.index',
          'parent_flag' => 'core.system',
        ),
        1 => 
        array (
          'name' => 'Create',
          'flag' => 'users.create',
          'parent_flag' => 'users.index',
        ),
        2 => 
        array (
          'name' => 'Edit',
          'flag' => 'users.edit',
          'parent_flag' => 'users.index',
        ),
        3 => 
        array (
          'name' => 'Delete',
          'flag' => 'users.destroy',
          'parent_flag' => 'users.index',
        ),
        4 => 
        array (
          'name' => 'Roles',
          'flag' => 'roles.index',
          'parent_flag' => 'core.system',
        ),
        5 => 
        array (
          'name' => 'Create',
          'flag' => 'roles.create',
          'parent_flag' => 'roles.index',
        ),
        6 => 
        array (
          'name' => 'Edit',
          'flag' => 'roles.edit',
          'parent_flag' => 'roles.index',
        ),
        7 => 
        array (
          'name' => 'Delete',
          'flag' => 'roles.destroy',
          'parent_flag' => 'roles.index',
        ),
      ),
      'email' => 
      array (
        'name' => 'core/acl::auth.settings.email.title',
        'description' => 'core/acl::auth.settings.email.description',
        'templates' => 
        array (
          'password-reminder' => 
          array (
            'title' => 'core/acl::auth.settings.email.templates.password_reminder.title',
            'description' => 'core/acl::auth.settings.email.templates.password_reminder.description',
            'subject' => 'core/acl::auth.settings.email.templates.password_reminder.subject',
            'can_off' => false,
            'variables' => 
            array (
              'reset_link' => 'core/acl::auth.settings.email.templates.password_reminder.reset_link',
            ),
          ),
        ),
      ),
    ),
    'media' => 
    array (
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Media',
          'flag' => 'media.index',
          'parent_flag' => 'core.cms',
        ),
        1 => 
        array (
          'name' => 'File',
          'flag' => 'files.index',
          'parent_flag' => 'media.index',
        ),
        2 => 
        array (
          'name' => 'Create',
          'flag' => 'files.create',
          'parent_flag' => 'files.index',
        ),
        3 => 
        array (
          'name' => 'Edit',
          'flag' => 'files.edit',
          'parent_flag' => 'files.index',
        ),
        4 => 
        array (
          'name' => 'Trash',
          'flag' => 'files.trash',
          'parent_flag' => 'files.index',
        ),
        5 => 
        array (
          'name' => 'Delete',
          'flag' => 'files.destroy',
          'parent_flag' => 'files.index',
        ),
        6 => 
        array (
          'name' => 'Folder',
          'flag' => 'folders.index',
          'parent_flag' => 'media.index',
        ),
        7 => 
        array (
          'name' => 'Create',
          'flag' => 'folders.create',
          'parent_flag' => 'folders.index',
        ),
        8 => 
        array (
          'name' => 'Edit',
          'flag' => 'folders.edit',
          'parent_flag' => 'folders.index',
        ),
        9 => 
        array (
          'name' => 'Trash',
          'flag' => 'folders.trash',
          'parent_flag' => 'folders.index',
        ),
        10 => 
        array (
          'name' => 'Delete',
          'flag' => 'folders.destroy',
          'parent_flag' => 'folders.index',
        ),
      ),
      'media' => 
      array (
        'sizes' => 
        array (
          'thumb' => '150x150',
        ),
        'permissions' => 
        array (
          0 => 'folders.create',
          1 => 'folders.edit',
          2 => 'folders.trash',
          3 => 'folders.destroy',
          4 => 'files.create',
          5 => 'files.edit',
          6 => 'files.trash',
          7 => 'files.destroy',
          8 => 'files.favorite',
          9 => 'folders.favorite',
        ),
        'libraries' => 
        array (
          'stylesheets' => 
          array (
            0 => 'vendor/core/core/media/libraries/jquery-context-menu/jquery.contextMenu.min.css',
            1 => 'vendor/core/core/media/css/media.css',
          ),
          'javascript' => 
          array (
            0 => 'vendor/core/core/media/libraries/lodash/lodash.min.js',
            1 => 'vendor/core/core/base/libraries/dropzone/dropzone.js',
            2 => 'vendor/core/core/media/libraries/jquery-context-menu/jquery.ui.position.min.js',
            3 => 'vendor/core/core/media/libraries/jquery-context-menu/jquery.contextMenu.min.js',
            4 => 'vendor/core/core/media/js/media.js',
          ),
        ),
        'allowed_mime_types' => 'jpg,jpeg,png,gif,txt,docx,zip,mp3,bmp,csv,xls,xlsx,ppt,pptx,pdf,mp4,m4v,doc,mpga,wav,webp,webm,mov,jfif,avif,rar,x-rar',
        'allowed_admin_to_upload_any_file_types' => false,
        'mime_types' => 
        array (
          'image' => 
          array (
            0 => 'image/png',
            1 => 'image/jpeg',
            2 => 'image/gif',
            3 => 'image/bmp',
            4 => 'image/svg+xml',
            5 => 'image/webp',
            6 => 'image/avif',
          ),
          'video' => 
          array (
            0 => 'video/mp4',
            1 => 'video/m4v',
            2 => 'video/mov',
            3 => 'video/quicktime',
          ),
          'document' => 
          array (
            0 => 'application/pdf',
            1 => 'application/vnd.ms-excel',
            2 => 'application/excel',
            3 => 'application/x-excel',
            4 => 'application/x-msexcel',
            5 => 'text/plain',
            6 => 'application/msword',
            7 => 'text/csv',
            8 => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            9 => 'application/vnd.ms-powerpoint',
            10 => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
          ),
          'zip' => 
          array (
            0 => 'application/zip',
            1 => 'application/x-zip-compressed',
            2 => 'application/x-compressed',
            3 => 'multipart/x-zip',
            4 => 'multipart/x-rar',
          ),
          'audio' => 
          array (
            0 => 'audio/mpeg',
            1 => 'audio/mp3',
            2 => 'audio/wav',
          ),
        ),
        'default_image' => '/vendor/core/core/base/images/placeholder.png',
        'sidebar_display' => 'horizontal',
        'watermark' => 
        array (
          'enabled' => 0,
          'source' => NULL,
          'size' => 10,
          'opacity' => 70,
          'position' => 'bottom-right',
          'x' => 10,
          'y' => 10,
        ),
        'custom_s3_path' => '',
        'chunk' => 
        array (
          'enabled' => false,
          'chunk_size' => 1048576,
          'max_file_size' => 1048576,
          'storage' => 
          array (
            'chunks' => 'chunks',
            'disk' => 'local',
          ),
          'clear' => 
          array (
            'timestamp' => '-3 HOURS',
            'schedule' => 
            array (
              'enabled' => true,
              'cron' => '25 * * * *',
            ),
          ),
          'chunk' => 
          array (
            'name' => 
            array (
              'use' => 
              array (
                'session' => true,
                'browser' => false,
              ),
            ),
          ),
        ),
        'preview' => 
        array (
          'document' => 
          array (
            'enabled' => true,
            'providers' => 
            array (
              'google' => 'https://docs.google.com/gview?embedded=true&url={url}',
              'microsoft' => 'https://view.officeapps.live.com/op/view.aspx?src={url}',
            ),
            'default' => 'microsoft',
            'type' => 'iframe',
            'mime_types' => 
            array (
              0 => 'application/pdf',
              1 => 'application/vnd.ms-excel',
              2 => 'application/excel',
              3 => 'application/x-excel',
              4 => 'application/x-msexcel',
              5 => 'application/msword',
              6 => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
              7 => 'application/vnd.ms-powerpoint',
              8 => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
              9 => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            ),
          ),
        ),
        'default_upload_folder' => NULL,
        'default_upload_url' => NULL,
        'generate_thumbnails_enabled' => true,
        'generate_thumbnails_chunk_limit' => 50,
        'folder_colors' => 
        array (
          0 => '#3498db',
          1 => '#2ecc71',
          2 => '#e74c3c',
          3 => '#f39c12',
          4 => '#9b59b6',
          5 => '#1abc9c',
          6 => '#34495e',
          7 => '#e67e22',
          8 => '#27ae60',
          9 => '#c0392b',
        ),
        'use_storage_symlink' => false,
      ),
    ),
    'js-validation' => 
    array (
      'js-validation' => 
      array (
        'view' => 'core/js-validation::bootstrap',
        'form_selector' => 'form',
        'focus_on_error' => false,
        'duration_animate' => 1000,
        'disable_remote_validation' => false,
        'remote_validation_field' => '_js_validation',
        'escape' => false,
        'ignore' => '',
      ),
    ),
  ),
  'debugbar' => 
  array (
    'enabled' => false,
    'capture_ajax' => false,
    'remote_sites_path' => '',
  ),
  'sanctum' => 
  array (
    'stateful' => 
    array (
      0 => 'localhost',
      1 => 'localhost:3000',
      2 => '127.0.0.1',
      3 => '127.0.0.1:8000',
      4 => '::1',
      5 => 'localhost',
    ),
    'guard' => 
    array (
      0 => 'web',
    ),
    'expiration' => NULL,
    'token_prefix' => '',
    'middleware' => 
    array (
      'authenticate_session' => 'Laravel\\Sanctum\\Http\\Middleware\\AuthenticateSession',
      'encrypt_cookies' => 'Illuminate\\Cookie\\Middleware\\EncryptCookies',
      'validate_csrf_token' => 'Illuminate\\Foundation\\Http\\Middleware\\ValidateCsrfToken',
    ),
  ),
  'excel' => 
  array (
    'exports' => 
    array (
      'chunk_size' => 1000,
      'pre_calculate_formulas' => false,
      'strict_null_comparison' => false,
      'csv' => 
      array (
        'delimiter' => ',',
        'enclosure' => '"',
        'line_ending' => '
',
        'use_bom' => true,
        'include_separator_line' => false,
        'excel_compatibility' => false,
        'output_encoding' => '',
        'test_auto_detect' => true,
      ),
      'properties' => 
      array (
        'creator' => '',
        'lastModifiedBy' => '',
        'title' => '',
        'description' => '',
        'subject' => '',
        'keywords' => '',
        'category' => '',
        'manager' => '',
        'company' => '',
      ),
    ),
    'imports' => 
    array (
      'read_only' => true,
      'ignore_empty' => true,
      'heading_row' => 
      array (
        'formatter' => 'slug',
      ),
      'csv' => 
      array (
        'delimiter' => NULL,
        'enclosure' => '"',
        'escape_character' => '\\',
        'contiguous' => false,
        'input_encoding' => 'UTF-8',
      ),
      'properties' => 
      array (
        'creator' => '',
        'lastModifiedBy' => '',
        'title' => '',
        'description' => '',
        'subject' => '',
        'keywords' => '',
        'category' => '',
        'manager' => '',
        'company' => '',
      ),
      'cells' => 
      array (
        'middleware' => 
        array (
        ),
      ),
    ),
    'extension_detector' => 
    array (
      'xlsx' => 'Xlsx',
      'xlsm' => 'Xlsx',
      'xltx' => 'Xlsx',
      'xltm' => 'Xlsx',
      'xls' => 'Xls',
      'xlt' => 'Xls',
      'ods' => 'Ods',
      'ots' => 'Ods',
      'slk' => 'Slk',
      'xml' => 'Xml',
      'gnumeric' => 'Gnumeric',
      'htm' => 'Html',
      'html' => 'Html',
      'csv' => 'Csv',
      'tsv' => 'Csv',
      'pdf' => 'Dompdf',
    ),
    'value_binder' => 
    array (
      'default' => 'Maatwebsite\\Excel\\DefaultValueBinder',
    ),
    'cache' => 
    array (
      'driver' => 'memory',
      'batch' => 
      array (
        'memory_limit' => 60000,
      ),
      'illuminate' => 
      array (
        'store' => NULL,
      ),
      'default_ttl' => 10800,
    ),
    'transactions' => 
    array (
      'handler' => 'db',
      'db' => 
      array (
        'connection' => NULL,
      ),
    ),
    'temporary_files' => 
    array (
      'local_path' => 'C:\\xampp\\htdocs\\main\\storage\\framework/cache/laravel-excel',
      'local_permissions' => 
      array (
      ),
      'remote_disk' => NULL,
      'remote_prefix' => NULL,
      'force_resync_remote' => NULL,
    ),
  ),
  'purifier' => 
  array (
    'encoding' => 'UTF-8',
    'finalize' => true,
    'ignoreNonStrings' => false,
    'cachePath' => 'C:\\xampp\\htdocs\\main\\storage\\app/purifier',
    'cacheFileMode' => 493,
    'settings' => 
    array (
      'default' => 
      array (
        'HTML.Doctype' => 'HTML 4.01 Transitional',
        'HTML.Allowed' => 'div,b,strong,i,em,u,a[href|title|rel|style|target|dofollow|nofollow],ul,ol,li,p[style],br,span[style],img[width|height|alt|src|style|loading],button,ins[style|data-ad-client|data-ad-slot|data-ad-format|data-full-width-responsive],video[src|type|width|height|preload|controls|autoplay|autostart|poster|id|class,muted,loop],meta[name|content|property],link[media|type|rel|href]',
        'HTML.AllowedElements' => 
        array (
          0 => 'a',
          1 => 'b',
          2 => 'blockquote',
          3 => 'br',
          4 => 'code',
          5 => 'em',
          6 => 'h1',
          7 => 'h2',
          8 => 'h3',
          9 => 'h4',
          10 => 'h5',
          11 => 'h6',
          12 => 'hr',
          13 => 'i',
          14 => 'img',
          15 => 'li',
          16 => 'ol',
          17 => 'p',
          18 => 'pre',
          19 => 's',
          20 => 'span',
          21 => 'strong',
          22 => 'sub',
          23 => 'sup',
          24 => 'table',
          25 => 'tbody',
          26 => 'td',
          27 => 'dl',
          28 => 'dt',
          29 => 'dd',
          30 => 'th',
          31 => 'thead',
          32 => 'tr',
          33 => 'u',
          34 => 'ul',
          35 => 'pre',
          36 => 'abbr',
          37 => 'kbd',
          38 => 'var',
          39 => 'samp',
          40 => 'hr',
          41 => 'iframe',
          42 => 'figure',
          43 => 'figcaption',
          44 => 'section',
          45 => 'article',
          46 => 'aside',
          47 => 'blockquote',
          48 => 'caption',
          49 => 'del',
          50 => 'div',
          51 => 'button',
          52 => 'ins',
          53 => 'video',
          54 => 'source',
          55 => 'meta',
          56 => 'link',
          57 => 'audio',
        ),
        'HTML.SafeIframe' => 'true',
        'Attr.AllowedFrameTargets' => 
        array (
          0 => '_blank',
        ),
        'CSS.AllowedProperties' => 
        array (
          0 => 'font',
          1 => 'font-size',
          2 => 'font-weight',
          3 => 'font-style',
          4 => 'font-family',
          5 => 'text-decoration',
          6 => 'padding-left',
          7 => 'color',
          8 => 'background-color',
          9 => 'text-align',
          10 => 'max-width',
          11 => 'border',
          12 => 'width',
          13 => 'line-height',
          14 => 'word-spacing',
          15 => 'border-style',
          16 => 'list-style-type',
          17 => 'border-color',
          18 => 'height',
          19 => 'min-width',
          20 => 'min-height',
          21 => 'max-height',
          22 => 'list-style',
          23 => 'margin',
          24 => 'margin-bottom',
          25 => 'margin-left',
          26 => 'margin-right',
          27 => 'margin-top',
          28 => 'padding',
          29 => 'height',
          30 => 'line-height',
          31 => 'border-collapse',
        ),
        'CSS.MaxImgLength' => NULL,
        'AutoFormat.AutoParagraph' => false,
        'AutoFormat.RemoveEmpty' => false,
        'Attr.EnableID' => true,
        'URI.SafeIframeRegexp' => '%^(http://|https://|//)(www.youtube.com/embed/|player.vimeo.com/video/|maps.google.com/maps|www.google.com/maps|docs.google.com/|drive.google.com/|view.officeapps.live.com/op/embed.aspx|onedrive.live.com/embed|open.spotify.com/embed|localhost)%',
      ),
      'test' => 
      array (
        'Attr.EnableID' => 'true',
      ),
      'youtube' => 
      array (
        'HTML.SafeIframe' => 'true',
        'URI.SafeIframeRegexp' => '%^(http://|https://|//)(www.youtube.com/embed/|player.vimeo.com/video/)%',
      ),
      'custom_definition' => 
      array (
        'id' => 'html5-definitions',
        'rev' => 1,
        'debug' => false,
        'elements' => 
        array (
          0 => 
          array (
            0 => 'section',
            1 => 'Block',
            2 => 'Flow',
            3 => 'Common',
          ),
          1 => 
          array (
            0 => 'nav',
            1 => 'Block',
            2 => 'Flow',
            3 => 'Common',
          ),
          2 => 
          array (
            0 => 'article',
            1 => 'Block',
            2 => 'Flow',
            3 => 'Common',
          ),
          3 => 
          array (
            0 => 'aside',
            1 => 'Block',
            2 => 'Flow',
            3 => 'Common',
          ),
          4 => 
          array (
            0 => 'header',
            1 => 'Block',
            2 => 'Flow',
            3 => 'Common',
          ),
          5 => 
          array (
            0 => 'footer',
            1 => 'Block',
            2 => 'Flow',
            3 => 'Common',
          ),
          6 => 
          array (
            0 => 'address',
            1 => 'Block',
            2 => 'Flow',
            3 => 'Common',
          ),
          7 => 
          array (
            0 => 'hgroup',
            1 => 'Block',
            2 => 'Required: h1 | h2 | h3 | h4 | h5 | h6',
            3 => 'Common',
          ),
          8 => 
          array (
            0 => 'figure',
            1 => 'Block',
            2 => 'Optional: (figcaption, Flow) | (Flow, figcaption) | Flow',
            3 => 'Common',
          ),
          9 => 
          array (
            0 => 'figcaption',
            1 => 'Inline',
            2 => 'Flow',
            3 => 'Common',
          ),
          10 => 
          array (
            0 => 'video',
            1 => 'Block',
            2 => 'Optional: (source, Flow) | (Flow, source) | Flow',
            3 => 'Common',
            4 => 
            array (
              'src' => 'URI',
              'type' => 'Text',
              'width' => 'Length',
              'height' => 'Length',
              'poster' => 'URI',
              'preload' => 'Enum#auto,metadata,none',
              'controls' => 'Bool',
            ),
          ),
          11 => 
          array (
            0 => 'source',
            1 => 'Block',
            2 => 'Flow',
            3 => 'Common',
            4 => 
            array (
              'src' => 'URI',
              'type' => 'Text',
            ),
          ),
          12 => 
          array (
            0 => 's',
            1 => 'Inline',
            2 => 'Inline',
            3 => 'Common',
          ),
          13 => 
          array (
            0 => 'var',
            1 => 'Inline',
            2 => 'Inline',
            3 => 'Common',
          ),
          14 => 
          array (
            0 => 'sub',
            1 => 'Inline',
            2 => 'Inline',
            3 => 'Common',
          ),
          15 => 
          array (
            0 => 'sup',
            1 => 'Inline',
            2 => 'Inline',
            3 => 'Common',
          ),
          16 => 
          array (
            0 => 'mark',
            1 => 'Inline',
            2 => 'Inline',
            3 => 'Common',
          ),
          17 => 
          array (
            0 => 'wbr',
            1 => 'Inline',
            2 => 'Empty',
            3 => 'Core',
          ),
          18 => 
          array (
            0 => 'ins',
            1 => 'Block',
            2 => 'Flow',
            3 => 'Common',
            4 => 
            array (
              'cite' => 'URI',
              'datetime' => 'CDATA',
            ),
          ),
          19 => 
          array (
            0 => 'del',
            1 => 'Block',
            2 => 'Flow',
            3 => 'Common',
            4 => 
            array (
              'cite' => 'URI',
              'datetime' => 'CDATA',
            ),
          ),
        ),
        'attributes' => 
        array (
          0 => 
          array (
            0 => 'iframe',
            1 => 'allowfullscreen',
            2 => 'Bool',
          ),
          1 => 
          array (
            0 => 'table',
            1 => 'height',
            2 => 'Text',
          ),
          2 => 
          array (
            0 => 'td',
            1 => 'border',
            2 => 'Text',
          ),
          3 => 
          array (
            0 => 'th',
            1 => 'border',
            2 => 'Text',
          ),
          4 => 
          array (
            0 => 'tr',
            1 => 'width',
            2 => 'Text',
          ),
          5 => 
          array (
            0 => 'tr',
            1 => 'height',
            2 => 'Text',
          ),
          6 => 
          array (
            0 => 'tr',
            1 => 'border',
            2 => 'Text',
          ),
        ),
      ),
      'custom_attributes' => 
      array (
        0 => 
        array (
          0 => 'a',
          1 => 'rel',
          2 => 'Text',
        ),
        1 => 
        array (
          0 => 'a',
          1 => 'dofollow',
          2 => 'Bool',
        ),
        2 => 
        array (
          0 => 'a',
          1 => 'nofollow',
          2 => 'Bool',
        ),
        3 => 
        array (
          0 => 'span',
          1 => 'data-period',
          2 => 'Text',
        ),
        4 => 
        array (
          0 => 'span',
          1 => 'data-type',
          2 => 'Text',
        ),
        5 => 
        array (
          0 => 'ins',
          1 => 'data-ad-client',
          2 => 'Text',
        ),
        6 => 
        array (
          0 => 'ins',
          1 => 'data-ad-slot',
          2 => 'Text',
        ),
        7 => 
        array (
          0 => 'ins',
          1 => 'data-ad-format',
          2 => 'Text',
        ),
        8 => 
        array (
          0 => 'ins',
          1 => 'data-ad-full-width-responsive',
          2 => 'Text',
        ),
        9 => 
        array (
          0 => 'img',
          1 => 'data-src',
          2 => 'Text',
        ),
        10 => 
        array (
          0 => 'img',
          1 => 'loading',
          2 => 'Text',
        ),
        11 => 
        array (
          0 => 'video',
          1 => 'autoplay',
          2 => 'Bool',
        ),
        12 => 
        array (
          0 => 'video',
          1 => 'muted',
          2 => 'Bool',
        ),
        13 => 
        array (
          0 => 'video',
          1 => 'loop',
          2 => 'Bool',
        ),
        14 => 
        array (
          0 => 'meta',
          1 => 'name',
          2 => 'Text',
        ),
        15 => 
        array (
          0 => 'meta',
          1 => 'content',
          2 => 'Text',
        ),
        16 => 
        array (
          0 => 'meta',
          1 => 'property',
          2 => 'Text',
        ),
        17 => 
        array (
          0 => 'link',
          1 => 'media',
          2 => 'Text',
        ),
        18 => 
        array (
          0 => 'link',
          1 => 'type',
          2 => 'Text',
        ),
        19 => 
        array (
          0 => 'link',
          1 => 'rel',
          2 => 'Text',
        ),
        20 => 
        array (
          0 => 'link',
          1 => 'href',
          2 => 'Text',
        ),
        21 => 
        array (
          0 => 'link',
          1 => 'color',
          2 => 'Text',
        ),
        22 => 
        array (
          0 => 'audio',
          1 => 'controls',
          2 => 'Bool',
        ),
        23 => 
        array (
          0 => 'div',
          1 => 'data-bs-theme',
          2 => 'Text',
        ),
        24 => 
        array (
          0 => 'div',
          1 => 'data-url',
          2 => 'Text',
        ),
        25 => 
        array (
          0 => 'button',
          1 => 'data-bb-toggle',
          2 => 'Text',
        ),
        26 => 
        array (
          0 => 'button',
          1 => 'data-value',
          2 => 'Text',
        ),
      ),
      'custom_elements' => 
      array (
        0 => 
        array (
          0 => 'u',
          1 => 'Inline',
          2 => 'Inline',
          3 => 'Common',
        ),
        1 => 
        array (
          0 => 'button',
          1 => 'Inline',
          2 => 'Inline',
          3 => 'Common',
        ),
        2 => 
        array (
          0 => 'ins',
          1 => 'Inline',
          2 => 'Inline',
          3 => 'Common',
        ),
        3 => 
        array (
          0 => 'meta',
          1 => 'Inline',
          2 => 'Empty',
          3 => 'Common',
        ),
        4 => 
        array (
          0 => 'link',
          1 => 'Inline',
          2 => 'Empty',
          3 => 'Common',
        ),
        5 => 
        array (
          0 => 'audio',
          1 => 'Block',
          2 => 'Optional: (source, Flow) | (Flow, source) | Flow',
          3 => 'Common',
        ),
      ),
    ),
  ),
  'mollie' => 
  array (
    'key' => NULL,
  ),
  'datatables-buttons' => 
  array (
    'namespace' => 
    array (
      'base' => 'DataTables',
      'model' => 'App\\Models',
    ),
    'pdf_generator' => 'excel',
    'snappy' => 
    array (
      'options' => 
      array (
        'no-outline' => true,
        'margin-left' => '0',
        'margin-right' => '0',
        'margin-top' => '10mm',
        'margin-bottom' => '10mm',
      ),
      'orientation' => 'landscape',
    ),
    'parameters' => 
    array (
      'dom' => 'Bfrtip',
      'order' => 
      array (
        0 => 
        array (
          0 => 0,
          1 => 'desc',
        ),
      ),
      'buttons' => 
      array (
        0 => 'excel',
        1 => 'csv',
        2 => 'pdf',
        3 => 'print',
        4 => 'reset',
        5 => 'reload',
      ),
    ),
    'generator' => 
    array (
      'columns' => 'id,add your columns,created_at,updated_at',
      'buttons' => 'excel,csv,pdf,print,reset,reload',
      'dom' => 'Bfrtip',
    ),
  ),
  'datatables-html' => 
  array (
    'namespace' => 'LaravelDataTables',
    'table' => 
    array (
      'class' => 'table',
      'id' => 'dataTableBuilder',
    ),
    'script' => 'datatables::script',
    'editor' => 'datatables::editor',
  ),
  'datatables' => 
  array (
    'search' => 
    array (
      'smart' => true,
      'multi_term' => true,
      'case_insensitive' => true,
      'use_wildcards' => false,
      'starts_with' => false,
    ),
    'index_column' => 'DT_RowIndex',
    'engines' => 
    array (
      'eloquent' => 'Botble\\Table\\EloquentDataTable',
      'query' => 'Botble\\Table\\QueryDataTable',
      'collection' => 'Botble\\Table\\CollectionDataTable',
      'resource' => 'Botble\\Table\\ApiResourceDataTable',
    ),
    'builders' => 
    array (
    ),
    'nulls_last_sql' => ':column :direction NULLS LAST',
    'error' => NULL,
    'columns' => 
    array (
      'excess' => 
      array (
        0 => 'rn',
        1 => 'row_num',
      ),
      'escape' => '*',
      'raw' => 
      array (
        0 => 'action',
      ),
      'blacklist' => 
      array (
        0 => 'password',
        1 => 'remember_token',
      ),
      'whitelist' => '*',
    ),
    'json' => 
    array (
      'header' => 
      array (
      ),
      'options' => 0,
    ),
    'callback' => 
    array (
      0 => '$',
      1 => '$.',
      2 => 'function',
    ),
  ),
  'packages' => 
  array (
    'api' => 
    array (
      'api' => 
      array (
        'provider' => 
        array (
          'model' => 'Botble\\ACL\\Models\\User',
          'guard' => 'web',
          'password_broker' => 'users',
          'verify_email' => false,
        ),
      ),
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'API',
          'flag' => 'api.settings',
          'parent_flag' => 'settings.index',
        ),
        1 => 
        array (
          'name' => 'Sanctum Token',
          'flag' => 'api.sanctum-token.index',
          'parent_flag' => 'api.settings',
        ),
        2 => 
        array (
          'name' => 'Create',
          'flag' => 'api.sanctum-token.create',
          'parent_flag' => 'api.sanctum-token.index',
        ),
        3 => 
        array (
          'name' => 'Delete',
          'flag' => 'api.sanctum-token.destroy',
          'parent_flag' => 'api.sanctum-token.index',
        ),
      ),
    ),
    'data-synchronize' => 
    array (
      'data-synchronize' => 
      array (
        'mime_types' => 
        array (
          0 => 'application/vnd.ms-excel',
          1 => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          2 => 'text/csv',
          3 => 'application/csv',
          4 => 'text/plain',
        ),
        'extensions' => 
        array (
          0 => 'csv',
          1 => 'xls',
          2 => 'xlsx',
        ),
        'storage' => 
        array (
          'disk' => 'local',
          'path' => 'data-synchronize',
        ),
      ),
    ),
    'installer' => 
    array (
      'installer' => 
      array (
        'enabled' => true,
        'requirements' => 
        array (
          'php' => 
          array (
            0 => 'openssl',
            1 => 'pdo',
            2 => 'mbstring',
            3 => 'tokenizer',
            4 => 'JSON',
            5 => 'cURL',
            6 => 'gd',
            7 => 'fileinfo',
            8 => 'xml',
            9 => 'ctype',
          ),
          'apache' => 
          array (
            0 => 'mod_rewrite',
          ),
          'permissions' => 
          array (
            0 => '.env',
            1 => 'storage/framework/',
            2 => 'storage/logs/',
            3 => 'bootstrap/cache/',
          ),
        ),
      ),
    ),
    'menu' => 
    array (
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Menu',
          'flag' => 'menus.index',
          'parent_flag' => 'core.appearance',
        ),
        1 => 
        array (
          'name' => 'Create',
          'flag' => 'menus.create',
          'parent_flag' => 'menus.index',
        ),
        2 => 
        array (
          'name' => 'Edit',
          'flag' => 'menus.edit',
          'parent_flag' => 'menus.index',
        ),
        3 => 
        array (
          'name' => 'Delete',
          'flag' => 'menus.destroy',
          'parent_flag' => 'menus.index',
        ),
      ),
      'general' => 
      array (
        'locations' => 
        array (
          'main-menu' => 'Main Navigation',
        ),
        'cache' => 
        array (
          'enabled' => false,
        ),
      ),
    ),
    'optimize' => 
    array (
      'general' => 
      array (
        'skip' => 
        array (
          0 => '*.xml',
          1 => '*.less',
          2 => '*.pdf',
          3 => '*.doc',
          4 => '*.txt',
          5 => '*.ico',
          6 => '*.rss',
          7 => '*.zip',
          8 => '*.mp3',
          9 => '*.rar',
          10 => '*.exe',
          11 => '*.wmv',
          12 => '*.doc',
          13 => '*.avi',
          14 => '*.ppt',
          15 => '*.mpg',
          16 => '*.mpeg',
          17 => '*.tif',
          18 => '*.wav',
          19 => '*.mov',
          20 => '*.psd',
          21 => '*.ai',
          22 => '*.xls',
          23 => '*.mp4',
          24 => '*.m4a',
          25 => '*.swf',
          26 => '*.dat',
          27 => '*.dmg',
          28 => '*.iso',
          29 => '*.flv',
          30 => '*.m4v',
          31 => '*.torrent',
        ),
      ),
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Optimize',
          'flag' => 'optimize.settings',
          'parent_flag' => 'settings.common',
        ),
      ),
    ),
    'page' => 
    array (
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Pages',
          'flag' => 'pages.index',
          'parent_flag' => 'core.cms',
        ),
        1 => 
        array (
          'name' => 'Create',
          'flag' => 'pages.create',
          'parent_flag' => 'pages.index',
        ),
        2 => 
        array (
          'name' => 'Edit',
          'flag' => 'pages.edit',
          'parent_flag' => 'pages.index',
        ),
        3 => 
        array (
          'name' => 'Delete',
          'flag' => 'pages.destroy',
          'parent_flag' => 'pages.index',
        ),
      ),
      'general' => 
      array (
        'templates' => 
        array (
          'default' => 'Default',
        ),
      ),
    ),
    'plugin-management' => 
    array (
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Plugins',
          'flag' => 'plugins.index',
          'parent_flag' => 'core.system',
        ),
        1 => 
        array (
          'name' => 'Activate/Deactivate',
          'flag' => 'plugins.edit',
          'parent_flag' => 'plugins.index',
        ),
        2 => 
        array (
          'name' => 'Remove',
          'flag' => 'plugins.remove',
          'parent_flag' => 'plugins.index',
        ),
        3 => 
        array (
          'name' => 'Add New Plugins',
          'flag' => 'plugins.marketplace',
          'parent_flag' => 'plugins.index',
        ),
      ),
      'general' => 
      array (
        'enable_plugin_manager' => true,
        'hide_plugin_author' => false,
        'enable_plugin_list_cache' => false,
        'enable_marketplace_feature' => true,
      ),
    ),
    'revision' => 
    array (
      'general' => 
      array (
        'supported' => 
        array (
          0 => 'Botble\\Page\\Models\\Page',
          1 => 'Botble\\Blog\\Models\\Post',
        ),
      ),
    ),
    'seo-helper' => 
    array (
      'general' => 
      array (
        'title' => 
        array (
          'separator' => '-',
          'first' => true,
          'max' => 120,
        ),
        'description' => 
        array (
          'max' => 386,
        ),
        'misc' => 
        array (
          'canonical' => true,
          'robots' => false,
          'default' => 
          array (
            'author' => '',
            'publisher' => '',
          ),
        ),
        'webmasters' => 
        array (
          'google' => '',
          'bing' => '',
          'alexa' => '',
          'pinterest' => '',
          'yandex' => '',
        ),
        'open-graph' => 
        array (
          'prefix' => 'og:',
          'type' => 'website',
          'properties' => 
          array (
          ),
        ),
        'twitter' => 
        array (
          'prefix' => 'twitter:',
          'card' => 'summary',
          'metas' => 
          array (
          ),
        ),
        'analytics' => 
        array (
          'google' => '',
        ),
        'supported' => 
        array (
          0 => 'Botble\\Page\\Models\\Page',
          1 => 'Botble\\Blog\\Models\\Post',
          2 => 'Botble\\Blog\\Models\\Category',
          3 => 'Botble\\Blog\\Models\\Tag',
          4 => 'Botble\\Ecommerce\\Models\\Product',
          5 => 'Botble\\Ecommerce\\Models\\Brand',
          6 => 'Botble\\Ecommerce\\Models\\ProductCategory',
          7 => 'Botble\\Ecommerce\\Models\\ProductTag',
          8 => 'Botble\\Gallery\\Models\\Gallery',
        ),
      ),
    ),
    'sitemap' => 
    array (
      'config' => 
      array (
        'use_cache' => false,
        'cache_key' => 'cms-sitemap.',
        'cache_duration' => 3600,
        'escaping' => true,
        'use_limit_size' => false,
        'max_size' => NULL,
        'use_styles' => true,
        'styles_location' => '/vendor/core/packages/sitemap/styles/',
        'use_gzip' => false,
      ),
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Sitemap',
          'flag' => 'sitemap.settings',
          'parent_flag' => 'settings.index',
        ),
      ),
    ),
    'slug' => 
    array (
      'general' => 
      array (
        'pattern' => '--slug--',
        'supported' => 
        array (
          'Botble\\Page\\Models\\Page' => 'Pages',
        ),
        'prefixes' => 
        array (
        ),
        'disable_preview' => 
        array (
        ),
        'slug_generated_columns' => 
        array (
        ),
        'enable_slug_translator' => false,
      ),
    ),
    'theme' => 
    array (
      'general' => 
      array (
        'themeDefault' => 'default',
        'layoutDefault' => 'default',
        'themeDir' => 'themes',
        'containerDir' => 
        array (
          'layout' => 'layouts',
          'asset' => '',
          'partial' => 'partials',
          'view' => 'views',
        ),
        'events' => 
        array (
        ),
        'enable_custom_js' => true,
        'enable_custom_html' => true,
        'enable_custom_html_shortcode' => true,
        'enable_robots_txt_editor' => true,
        'public_theme_name' => NULL,
        'display_theme_manager_in_admin_panel' => true,
        'public_single_ending_url' => NULL,
        'extra_date_format' => NULL,
      ),
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Appearance',
          'flag' => 'core.appearance',
          'parent_flag' => 'core.system',
        ),
        1 => 
        array (
          'name' => 'Theme',
          'flag' => 'theme.index',
          'parent_flag' => 'core.appearance',
        ),
        2 => 
        array (
          'name' => 'Activate',
          'flag' => 'theme.activate',
          'parent_flag' => 'theme.index',
        ),
        3 => 
        array (
          'name' => 'Remove',
          'flag' => 'theme.remove',
          'parent_flag' => 'theme.index',
        ),
        4 => 
        array (
          'name' => 'Theme options',
          'flag' => 'theme.options',
          'parent_flag' => 'core.appearance',
        ),
        5 => 
        array (
          'name' => 'Custom CSS',
          'flag' => 'theme.custom-css',
          'parent_flag' => 'core.appearance',
        ),
        6 => 
        array (
          'name' => 'Custom JS',
          'flag' => 'theme.custom-js',
          'parent_flag' => 'core.appearance',
        ),
        7 => 
        array (
          'name' => 'Custom HTML',
          'flag' => 'theme.custom-html',
          'parent_flag' => 'core.appearance',
        ),
        8 => 
        array (
          'name' => 'Robots.txt Editor',
          'flag' => 'theme.robots-txt',
          'parent_flag' => 'core.appearance',
        ),
        9 => 
        array (
          'name' => 'Website Tracking',
          'flag' => 'settings.website-tracking',
          'parent_flag' => 'settings.common',
        ),
      ),
    ),
    'widget' => 
    array (
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Widgets',
          'flag' => 'widgets.index',
          'parent_flag' => 'core.appearance',
        ),
      ),
    ),
  ),
  'assets' => 
  array (
    'offline' => true,
    'enable_version' => false,
    'version' => 1749151612,
    'scripts' => 
    array (
      0 => 'modernizr',
      1 => 'app',
    ),
    'styles' => 
    array (
      0 => 'bootstrap',
    ),
    'resources' => 
    array (
      'scripts' => 
      array (
        'app' => 
        array (
          'use_cdn' => false,
          'location' => 'footer',
          'src' => 
          array (
            'local' => '/js/app.js',
          ),
        ),
        'modernizr' => 
        array (
          'use_cdn' => true,
          'location' => 'header',
          'src' => 
          array (
            'local' => '/vendor/core/packages/modernizr/modernizr.min.js',
            'cdn' => '//cdnjs.cloudflare.com/ajax/libs/modernizr/2.8.3/modernizr.js',
          ),
        ),
      ),
      'styles' => 
      array (
        'bootstrap' => 
        array (
          'use_cdn' => true,
          'location' => 'header',
          'src' => 
          array (
            'local' => '/packages/bootstrap/css/bootstrap.min.css',
            'cdn' => '//stackpath.bootstrapcdn.com/bootstrap/4.1.1/css/bootstrap.min.css',
          ),
          'attributes' => 
          array (
            'integrity' => 'sha384-WskhaSGFgHYWDcbwN70/dfYBj47jz9qbsMId/iRN3ewGhXQFZCSftd1LZCfmhktB',
            'crossorigin' => 'anonymous',
          ),
        ),
      ),
    ),
  ),
  'ziggy' => 
  array (
    'except' => 
    array (
      0 => 'debugbar.*',
    ),
  ),
  'plugins' => 
  array (
    'language' => 
    array (
      'general' => 
      array (
        'supported' => 
        array (
          0 => 'Botble\\Menu\\Models\\Menu',
          1 => 'Botble\\Menu\\Models\\MenuNode',
          2 => 'Botble\\SimpleSlider\\Models\\SimpleSlider',
        ),
      ),
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Languages',
          'flag' => 'languages.index',
          'parent_flag' => 'settings.common',
        ),
        1 => 
        array (
          'name' => 'Create',
          'flag' => 'languages.create',
          'parent_flag' => 'languages.index',
        ),
        2 => 
        array (
          'name' => 'Edit',
          'flag' => 'languages.edit',
          'parent_flag' => 'languages.index',
        ),
        3 => 
        array (
          'name' => 'Delete',
          'flag' => 'languages.destroy',
          'parent_flag' => 'languages.index',
        ),
      ),
    ),
    'language-advanced' => 
    array (
      'general' => 
      array (
        'supported' => 
        array (
          'Botble\\Page\\Models\\Page' => 
          array (
            0 => 'name',
            1 => 'description',
            2 => 'content',
            3 => 'faq_schema_config',
            4 => 'faq_ids',
            5 => 'gallery',
          ),
          'Botble\\Slug\\Models\\Slug' => 
          array (
            0 => 'key',
            1 => 'prefix',
          ),
          'Botble\\Ads\\Models\\Ads' => 
          array (
            0 => 'name',
            1 => 'image',
            2 => 'tablet_image',
            3 => 'mobile_image',
            4 => 'url',
          ),
          'ArchiElite\\Announcement\\Models\\Announcement' => 
          array (
            0 => 'content',
            1 => 'action_label',
          ),
          'Botble\\Blog\\Models\\Post' => 
          array (
            0 => 'name',
            1 => 'description',
            2 => 'content',
            3 => 'faq_schema_config',
            4 => 'faq_ids',
            5 => 'gallery',
          ),
          'Botble\\Blog\\Models\\Category' => 
          array (
            0 => 'name',
            1 => 'description',
          ),
          'Botble\\Blog\\Models\\Tag' => 
          array (
            0 => 'name',
            1 => 'description',
          ),
          'Botble\\Contact\\Models\\CustomField' => 
          array (
            0 => 'name',
            1 => 'placeholder',
          ),
          'Botble\\Contact\\Models\\CustomFieldOption' => 
          array (
            0 => 'label',
          ),
          'Botble\\Ecommerce\\Models\\Product' => 
          array (
            0 => 'name',
            1 => 'description',
            2 => 'content',
            3 => 'faq_schema_config',
          ),
          'Botble\\Ecommerce\\Models\\SpecificationAttribute' => 
          array (
            0 => 'name',
            1 => 'options',
            2 => 'default_value',
          ),
          'Botble\\Ecommerce\\Models\\ProductCategory' => 
          array (
            0 => 'name',
            1 => 'description',
          ),
          'Botble\\Ecommerce\\Models\\ProductAttribute' => 
          array (
            0 => 'title',
            1 => 'attributes',
          ),
          'Botble\\Ecommerce\\Models\\ProductAttributeSet' => 
          array (
            0 => 'title',
          ),
          'Botble\\Ecommerce\\Models\\Brand' => 
          array (
            0 => 'name',
            1 => 'description',
          ),
          'Botble\\Ecommerce\\Models\\ProductCollection' => 
          array (
            0 => 'name',
            1 => 'description',
          ),
          'Botble\\Ecommerce\\Models\\ProductLabel' => 
          array (
            0 => 'name',
            1 => 'description',
          ),
          'Botble\\Ecommerce\\Models\\FlashSale' => 
          array (
            0 => 'name',
            1 => 'description',
          ),
          'Botble\\Ecommerce\\Models\\ProductTag' => 
          array (
            0 => 'name',
          ),
          'Botble\\Ecommerce\\Models\\Tax' => 
          array (
            0 => 'title',
          ),
          'Botble\\Ecommerce\\Models\\GlobalOption' => 
          array (
            0 => 'name',
          ),
          'Botble\\Ecommerce\\Models\\Option' => 
          array (
            0 => 'name',
          ),
          'Botble\\Ecommerce\\Models\\GlobalOptionValue' => 
          array (
            0 => 'option_value',
          ),
          'Botble\\Ecommerce\\Models\\OptionValue' => 
          array (
            0 => 'option_value',
          ),
          'Botble\\Faq\\Models\\Faq' => 
          array (
            0 => 'question',
            1 => 'answer',
          ),
          'Botble\\Faq\\Models\\FaqCategory' => 
          array (
            0 => 'name',
          ),
          'Botble\\Gallery\\Models\\Gallery' => 
          array (
            0 => 'name',
            1 => 'description',
            2 => 'gallery',
          ),
          'Botble\\Gallery\\Models\\GalleryMeta' => 
          array (
            0 => 'images',
          ),
          'Botble\\Location\\Models\\Country' => 
          array (
            0 => 'name',
            1 => 'nationality',
          ),
          'Botble\\Location\\Models\\State' => 
          array (
            0 => 'name',
          ),
          'Botble\\Location\\Models\\City' => 
          array (
            0 => 'name',
          ),
          'Botble\\Team\\Models\\Team' => 
          array (
            0 => 'name',
            1 => 'title',
            2 => 'location',
            3 => 'content',
            4 => 'phone',
            5 => 'address',
          ),
          'Botble\\Testimonial\\Models\\Testimonial' => 
          array (
            0 => 'name',
            1 => 'content',
            2 => 'company',
          ),
        ),
        'translatable_meta_boxes' => 
        array (
          0 => 'language_advanced_wrap',
          1 => 'seo_wrap',
          2 => 'contact-custom-field-options',
          3 => 'specification-attribute-options',
          4 => 'faq_schema_config_wrapper',
          5 => 'attributes_list',
          6 => 'product_options_box',
          7 => 'faq_schema_config_wrapper',
          8 => 'gallery_wrap',
        ),
        'page_use_language_v2' => true,
      ),
    ),
    'ads' => 
    array (
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Ads',
          'flag' => 'ads.index',
        ),
        1 => 
        array (
          'name' => 'Create',
          'flag' => 'ads.create',
          'parent_flag' => 'ads.index',
        ),
        2 => 
        array (
          'name' => 'Edit',
          'flag' => 'ads.edit',
          'parent_flag' => 'ads.index',
        ),
        3 => 
        array (
          'name' => 'Delete',
          'flag' => 'ads.destroy',
          'parent_flag' => 'ads.index',
        ),
        4 => 
        array (
          'name' => 'Ads',
          'flag' => 'ads.settings',
          'parent_flag' => 'settings.others',
        ),
      ),
      'general' => 
      array (
        'use_real_image_url' => true,
      ),
    ),
    'analytics' => 
    array (
      'general' => 
      array (
        'cache_lifetime_in_minutes' => 1440,
        'cache' => 
        array (
          'store' => 'file',
        ),
        'enabled_dashboard_widgets' => true,
      ),
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Analytics',
          'flag' => 'analytics.general',
          'parent_flag' => 'core.system',
        ),
        1 => 
        array (
          'name' => 'Top Page',
          'flag' => 'analytics.page',
          'parent_flag' => 'analytics.general',
        ),
        2 => 
        array (
          'name' => 'Top Browser',
          'flag' => 'analytics.browser',
          'parent_flag' => 'analytics.general',
        ),
        3 => 
        array (
          'name' => 'Top Referrer',
          'flag' => 'analytics.referrer',
          'parent_flag' => 'analytics.general',
        ),
        4 => 
        array (
          'name' => 'Analytics',
          'flag' => 'analytics.settings',
          'parent_flag' => 'settings.others',
        ),
      ),
    ),
    'announcement' => 
    array (
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Announcements',
          'flag' => 'announcements.index',
        ),
        1 => 
        array (
          'name' => 'Create',
          'flag' => 'announcements.create',
          'parent_flag' => 'announcements.index',
        ),
        2 => 
        array (
          'name' => 'Edit',
          'flag' => 'announcements.edit',
          'parent_flag' => 'announcements.index',
        ),
        3 => 
        array (
          'name' => 'Delete',
          'flag' => 'announcements.destroy',
          'parent_flag' => 'announcements.index',
        ),
        4 => 
        array (
          'name' => 'Announcements',
          'flag' => 'announcements.settings',
          'parent_flag' => 'settings.others',
        ),
      ),
    ),
    'audit-log' => 
    array (
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Activity Logs',
          'flag' => 'audit-log.index',
          'parent_flag' => 'core.system',
        ),
        1 => 
        array (
          'name' => 'Delete',
          'flag' => 'audit-log.destroy',
          'parent_flag' => 'audit-log.index',
        ),
      ),
    ),
    'backup' => 
    array (
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Backup',
          'flag' => 'backups.index',
          'parent_flag' => 'core.system',
        ),
        1 => 
        array (
          'name' => 'Create',
          'flag' => 'backups.create',
          'parent_flag' => 'backups.index',
        ),
        2 => 
        array (
          'name' => 'Restore',
          'flag' => 'backups.restore',
          'parent_flag' => 'backups.index',
        ),
        3 => 
        array (
          'name' => 'Delete',
          'flag' => 'backups.destroy',
          'parent_flag' => 'backups.index',
        ),
      ),
      'general' => 
      array (
        'mysql' => 
        array (
          'execute_path' => '',
        ),
        'pgsql' => 
        array (
          'execute_path' => '',
        ),
      ),
    ),
    'blog' => 
    array (
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Blog',
          'flag' => 'plugins.blog',
          'parent_flag' => 'core.cms',
        ),
        1 => 
        array (
          'name' => 'Posts',
          'flag' => 'posts.index',
          'parent_flag' => 'plugins.blog',
        ),
        2 => 
        array (
          'name' => 'Create',
          'flag' => 'posts.create',
          'parent_flag' => 'posts.index',
        ),
        3 => 
        array (
          'name' => 'Edit',
          'flag' => 'posts.edit',
          'parent_flag' => 'posts.index',
        ),
        4 => 
        array (
          'name' => 'Delete',
          'flag' => 'posts.destroy',
          'parent_flag' => 'posts.index',
        ),
        5 => 
        array (
          'name' => 'Categories',
          'flag' => 'categories.index',
          'parent_flag' => 'plugins.blog',
        ),
        6 => 
        array (
          'name' => 'Create',
          'flag' => 'categories.create',
          'parent_flag' => 'categories.index',
        ),
        7 => 
        array (
          'name' => 'Edit',
          'flag' => 'categories.edit',
          'parent_flag' => 'categories.index',
        ),
        8 => 
        array (
          'name' => 'Delete',
          'flag' => 'categories.destroy',
          'parent_flag' => 'categories.index',
        ),
        9 => 
        array (
          'name' => 'Tags',
          'flag' => 'tags.index',
          'parent_flag' => 'plugins.blog',
        ),
        10 => 
        array (
          'name' => 'Create',
          'flag' => 'tags.create',
          'parent_flag' => 'tags.index',
        ),
        11 => 
        array (
          'name' => 'Edit',
          'flag' => 'tags.edit',
          'parent_flag' => 'tags.index',
        ),
        12 => 
        array (
          'name' => 'Delete',
          'flag' => 'tags.destroy',
          'parent_flag' => 'tags.index',
        ),
        13 => 
        array (
          'name' => 'Blog',
          'flag' => 'blog.settings',
          'parent_flag' => 'settings.others',
        ),
        14 => 
        array (
          'name' => 'Export Posts',
          'flag' => 'posts.export',
          'parent_flag' => 'tools.data-synchronize',
        ),
        15 => 
        array (
          'name' => 'Import Posts',
          'flag' => 'posts.import',
          'parent_flag' => 'tools.data-synchronize',
        ),
      ),
      'general' => 
      array (
        'use_language_v2' => true,
      ),
    ),
    'captcha' => 
    array (
      'general' => 
      array (
        'math-captcha' => 
        array (
          'operands' => 
          array (
            0 => '+',
            1 => '-',
            2 => '*',
          ),
          'rand-min' => 2,
          'rand-max' => 5,
        ),
      ),
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Captcha',
          'flag' => 'captcha.settings',
          'parent_flag' => 'settings.others',
        ),
      ),
    ),
    'contact' => 
    array (
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Contact',
          'flag' => 'contacts.index',
          'parent_flag' => 'core.cms',
        ),
        1 => 
        array (
          'name' => 'Edit',
          'flag' => 'contacts.edit',
          'parent_flag' => 'contacts.index',
        ),
        2 => 
        array (
          'name' => 'Delete',
          'flag' => 'contacts.destroy',
          'parent_flag' => 'contacts.index',
        ),
        3 => 
        array (
          'name' => 'Custom Fields',
          'flag' => 'contact.custom-fields',
          'parent_flag' => 'contacts.index',
        ),
        4 => 
        array (
          'name' => 'Contact',
          'flag' => 'contact.settings',
          'parent_flag' => 'settings.others',
        ),
      ),
      'email' => 
      array (
        'name' => 'plugins/contact::contact.settings.email.title',
        'description' => 'plugins/contact::contact.settings.email.description',
        'templates' => 
        array (
          'notice' => 
          array (
            'title' => 'plugins/contact::contact.settings.email.templates.notice_title',
            'description' => 'plugins/contact::contact.settings.email.templates.notice_description',
            'subject' => 'plugins/contact::contact.settings.email.templates.subject',
            'can_off' => true,
            'variables' => 
            array (
              'contact_name' => 'plugins/contact::contact.settings.email.templates.contact_name',
              'contact_subject' => 'plugins/contact::contact.settings.email.templates.contact_subject',
              'contact_email' => 'plugins/contact::contact.settings.email.templates.contact_email',
              'contact_phone' => 'plugins/contact::contact.settings.email.templates.contact_phone',
              'contact_address' => 'plugins/contact::contact.settings.email.templates.contact_address',
              'contact_content' => 'plugins/contact::contact.settings.email.templates.contact_content',
              'contact_custom_fields' => 'plugins/contact::contact.settings.email.templates.contact_custom_fields',
            ),
          ),
          'sender-confirmation' => 
          array (
            'title' => 'plugins/contact::contact.settings.email.templates.sender_confirmation_title',
            'description' => 'plugins/contact::contact.settings.email.templates.sender_confirmation_description',
            'subject' => 'plugins/contact::contact.settings.email.templates.sender_confirmation_subject',
            'can_off' => true,
            'enabled' => false,
            'variables' => 
            array (
              'contact_name' => 'plugins/contact::contact.settings.email.templates.contact_name',
              'contact_subject' => 'plugins/contact::contact.settings.email.templates.contact_subject',
              'contact_email' => 'plugins/contact::contact.settings.email.templates.contact_email',
              'contact_phone' => 'plugins/contact::contact.settings.email.templates.contact_phone',
              'contact_address' => 'plugins/contact::contact.settings.email.templates.contact_address',
              'contact_content' => 'plugins/contact::contact.settings.email.templates.contact_content',
              'contact_custom_fields' => 'plugins/contact::contact.settings.email.templates.contact_custom_fields',
            ),
          ),
        ),
      ),
    ),
    'cookie-consent' => 
    array (
      'general' => 
      array (
        'cookie_name' => 'cookie_for_consent',
        'cookie_lifetime' => 7300,
        'cookie_categories' => 
        array (
          'essential' => 
          array (
            'required' => true,
          ),
          'analytics' => 
          array (
            'required' => false,
          ),
          'marketing' => 
          array (
            'required' => false,
          ),
        ),
      ),
    ),
    'ecommerce' => 
    array (
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'E-commerce',
          'flag' => 'plugins.ecommerce',
        ),
        1 => 
        array (
          'name' => 'Reports',
          'flag' => 'ecommerce.report.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        2 => 
        array (
          'name' => 'Products',
          'flag' => 'products.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        3 => 
        array (
          'name' => 'Create',
          'flag' => 'products.create',
          'parent_flag' => 'products.index',
        ),
        4 => 
        array (
          'name' => 'Edit',
          'flag' => 'products.edit',
          'parent_flag' => 'products.index',
        ),
        5 => 
        array (
          'name' => 'Delete',
          'flag' => 'products.destroy',
          'parent_flag' => 'products.index',
        ),
        6 => 
        array (
          'name' => 'Duplicate',
          'flag' => 'products.duplicate',
          'parent_flag' => 'products.index',
        ),
        7 => 
        array (
          'name' => 'Product Prices',
          'flag' => 'ecommerce.product-prices.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        8 => 
        array (
          'name' => 'Update',
          'flag' => 'ecommerce.product-prices.edit',
          'parent_flag' => 'ecommerce.product-prices.index',
        ),
        9 => 
        array (
          'name' => 'Product Inventory',
          'flag' => 'ecommerce.product-inventory.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        10 => 
        array (
          'name' => 'Update',
          'flag' => 'ecommerce.product-inventory.edit',
          'parent_flag' => 'ecommerce.product-inventory.index',
        ),
        11 => 
        array (
          'name' => 'Product categories',
          'flag' => 'product-categories.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        12 => 
        array (
          'name' => 'Create',
          'flag' => 'product-categories.create',
          'parent_flag' => 'product-categories.index',
        ),
        13 => 
        array (
          'name' => 'Edit',
          'flag' => 'product-categories.edit',
          'parent_flag' => 'product-categories.index',
        ),
        14 => 
        array (
          'name' => 'Delete',
          'flag' => 'product-categories.destroy',
          'parent_flag' => 'product-categories.index',
        ),
        15 => 
        array (
          'name' => 'Product tags',
          'flag' => 'product-tag.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        16 => 
        array (
          'name' => 'Create',
          'flag' => 'product-tag.create',
          'parent_flag' => 'product-tag.index',
        ),
        17 => 
        array (
          'name' => 'Edit',
          'flag' => 'product-tag.edit',
          'parent_flag' => 'product-tag.index',
        ),
        18 => 
        array (
          'name' => 'Delete',
          'flag' => 'product-tag.destroy',
          'parent_flag' => 'product-tag.index',
        ),
        19 => 
        array (
          'name' => 'Brands',
          'flag' => 'brands.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        20 => 
        array (
          'name' => 'Create',
          'flag' => 'brands.create',
          'parent_flag' => 'brands.index',
        ),
        21 => 
        array (
          'name' => 'Edit',
          'flag' => 'brands.edit',
          'parent_flag' => 'brands.index',
        ),
        22 => 
        array (
          'name' => 'Delete',
          'flag' => 'brands.destroy',
          'parent_flag' => 'brands.index',
        ),
        23 => 
        array (
          'name' => 'Product collections',
          'flag' => 'product-collections.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        24 => 
        array (
          'name' => 'Create',
          'flag' => 'product-collections.create',
          'parent_flag' => 'product-collections.index',
        ),
        25 => 
        array (
          'name' => 'Edit',
          'flag' => 'product-collections.edit',
          'parent_flag' => 'product-collections.index',
        ),
        26 => 
        array (
          'name' => 'Delete',
          'flag' => 'product-collections.destroy',
          'parent_flag' => 'product-collections.index',
        ),
        27 => 
        array (
          'name' => 'Product Attributes Sets',
          'flag' => 'product-attribute-sets.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        28 => 
        array (
          'name' => 'Create',
          'flag' => 'product-attribute-sets.create',
          'parent_flag' => 'product-attribute-sets.index',
        ),
        29 => 
        array (
          'name' => 'Edit',
          'flag' => 'product-attribute-sets.edit',
          'parent_flag' => 'product-attribute-sets.index',
        ),
        30 => 
        array (
          'name' => 'Delete',
          'flag' => 'product-attribute-sets.destroy',
          'parent_flag' => 'product-attribute-sets.index',
        ),
        31 => 
        array (
          'name' => 'Product Attributes',
          'flag' => 'product-attributes.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        32 => 
        array (
          'name' => 'Create',
          'flag' => 'product-attributes.create',
          'parent_flag' => 'product-attributes.index',
        ),
        33 => 
        array (
          'name' => 'Edit',
          'flag' => 'product-attributes.edit',
          'parent_flag' => 'product-attributes.index',
        ),
        34 => 
        array (
          'name' => 'Delete',
          'flag' => 'product-attributes.destroy',
          'parent_flag' => 'product-attributes.index',
        ),
        35 => 
        array (
          'name' => 'Taxes',
          'flag' => 'tax.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        36 => 
        array (
          'name' => 'Create',
          'flag' => 'tax.create',
          'parent_flag' => 'tax.index',
        ),
        37 => 
        array (
          'name' => 'Edit',
          'flag' => 'tax.edit',
          'parent_flag' => 'tax.index',
        ),
        38 => 
        array (
          'name' => 'Delete',
          'flag' => 'tax.destroy',
          'parent_flag' => 'tax.index',
        ),
        39 => 
        array (
          'name' => 'Reviews',
          'flag' => 'reviews.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        40 => 
        array (
          'name' => 'Create',
          'flag' => 'reviews.create',
          'parent_flag' => 'reviews.index',
        ),
        41 => 
        array (
          'name' => 'Delete',
          'flag' => 'reviews.destroy',
          'parent_flag' => 'reviews.index',
        ),
        42 => 
        array (
          'name' => 'Publish/Unpublish Review',
          'flag' => 'reviews.publish',
          'parent_flag' => 'reviews.index',
        ),
        43 => 
        array (
          'name' => 'Reply Review',
          'flag' => 'reviews.reply',
          'parent_flag' => 'reviews.index',
        ),
        44 => 
        array (
          'name' => 'Shipments',
          'flag' => 'ecommerce.shipments.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        45 => 
        array (
          'name' => 'Create',
          'flag' => 'ecommerce.shipments.create',
          'parent_flag' => 'ecommerce.shipments.index',
        ),
        46 => 
        array (
          'name' => 'Edit',
          'flag' => 'ecommerce.shipments.edit',
          'parent_flag' => 'ecommerce.shipments.index',
        ),
        47 => 
        array (
          'name' => 'Delete',
          'flag' => 'ecommerce.shipments.destroy',
          'parent_flag' => 'ecommerce.shipments.index',
        ),
        48 => 
        array (
          'name' => 'Orders',
          'flag' => 'orders.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        49 => 
        array (
          'name' => 'Create',
          'flag' => 'orders.create',
          'parent_flag' => 'orders.index',
        ),
        50 => 
        array (
          'name' => 'Edit',
          'flag' => 'orders.edit',
          'parent_flag' => 'orders.index',
        ),
        51 => 
        array (
          'name' => 'Delete',
          'flag' => 'orders.destroy',
          'parent_flag' => 'orders.index',
        ),
        52 => 
        array (
          'name' => 'Discounts',
          'flag' => 'discounts.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        53 => 
        array (
          'name' => 'Create',
          'flag' => 'discounts.create',
          'parent_flag' => 'discounts.index',
        ),
        54 => 
        array (
          'name' => 'Edit',
          'flag' => 'discounts.edit',
          'parent_flag' => 'discounts.index',
        ),
        55 => 
        array (
          'name' => 'Delete',
          'flag' => 'discounts.destroy',
          'parent_flag' => 'discounts.index',
        ),
        56 => 
        array (
          'name' => 'Customers',
          'flag' => 'customers.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        57 => 
        array (
          'name' => 'Create',
          'flag' => 'customers.create',
          'parent_flag' => 'customers.index',
        ),
        58 => 
        array (
          'name' => 'Edit',
          'flag' => 'customers.edit',
          'parent_flag' => 'customers.index',
        ),
        59 => 
        array (
          'name' => 'Delete',
          'flag' => 'customers.destroy',
          'parent_flag' => 'customers.index',
        ),
        60 => 
        array (
          'name' => 'Flash sales',
          'flag' => 'flash-sale.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        61 => 
        array (
          'name' => 'Create',
          'flag' => 'flash-sale.create',
          'parent_flag' => 'flash-sale.index',
        ),
        62 => 
        array (
          'name' => 'Edit',
          'flag' => 'flash-sale.edit',
          'parent_flag' => 'flash-sale.index',
        ),
        63 => 
        array (
          'name' => 'Delete',
          'flag' => 'flash-sale.destroy',
          'parent_flag' => 'flash-sale.index',
        ),
        64 => 
        array (
          'name' => 'Product labels',
          'flag' => 'product-label.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        65 => 
        array (
          'name' => 'Create',
          'flag' => 'product-label.create',
          'parent_flag' => 'product-label.index',
        ),
        66 => 
        array (
          'name' => 'Edit',
          'flag' => 'product-label.edit',
          'parent_flag' => 'product-label.index',
        ),
        67 => 
        array (
          'name' => 'Delete',
          'flag' => 'product-label.destroy',
          'parent_flag' => 'product-label.index',
        ),
        68 => 
        array (
          'name' => 'Import Products',
          'flag' => 'ecommerce.import.products.index',
          'parent_flag' => 'tools.data-synchronize',
        ),
        69 => 
        array (
          'name' => 'Export Products',
          'flag' => 'ecommerce.export.products.index',
          'parent_flag' => 'tools.data-synchronize',
        ),
        70 => 
        array (
          'name' => 'Order Returns',
          'flag' => 'order_returns.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        71 => 
        array (
          'name' => 'Edit',
          'flag' => 'order_returns.edit',
          'parent_flag' => 'order_returns.index',
        ),
        72 => 
        array (
          'name' => 'Delete',
          'flag' => 'order_returns.destroy',
          'parent_flag' => 'order_returns.index',
        ),
        73 => 
        array (
          'name' => 'Product Options',
          'flag' => 'global-option.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        74 => 
        array (
          'name' => 'Create',
          'flag' => 'global-option.create',
          'parent_flag' => 'global-option.index',
        ),
        75 => 
        array (
          'name' => 'Edit',
          'flag' => 'global-option.edit',
          'parent_flag' => 'global-option.index',
        ),
        76 => 
        array (
          'name' => 'Delete',
          'flag' => 'global-option.destroy',
          'parent_flag' => 'global-option.index',
        ),
        77 => 
        array (
          'name' => 'Invoices',
          'flag' => 'ecommerce.invoice.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        78 => 
        array (
          'name' => 'Edit',
          'flag' => 'ecommerce.invoice.edit',
          'parent_flag' => 'ecommerce.invoice.index',
        ),
        79 => 
        array (
          'name' => 'Delete',
          'flag' => 'ecommerce.invoice.destroy',
          'parent_flag' => 'ecommerce.invoice.index',
        ),
        80 => 
        array (
          'name' => 'Ecommerce',
          'flag' => 'ecommerce.settings',
          'parent_flag' => 'settings.index',
        ),
        81 => 
        array (
          'name' => 'General',
          'flag' => 'ecommerce.settings.general',
          'parent_flag' => 'ecommerce.settings',
        ),
        82 => 
        array (
          'name' => 'Invoice Template',
          'flag' => 'ecommerce.invoice-template.index',
          'parent_flag' => 'ecommerce.settings',
        ),
        83 => 
        array (
          'name' => 'Currencies',
          'flag' => 'ecommerce.settings.currencies',
          'parent_flag' => 'ecommerce.settings',
        ),
        84 => 
        array (
          'name' => 'Product',
          'flag' => 'ecommerce.settings.products',
          'parent_flag' => 'ecommerce.settings',
        ),
        85 => 
        array (
          'name' => 'Product Search',
          'flag' => 'ecommerce.settings.product-search',
          'parent_flag' => 'ecommerce.settings',
        ),
        86 => 
        array (
          'name' => 'Digital Product',
          'flag' => 'ecommerce.settings.digital-products',
          'parent_flag' => 'ecommerce.settings',
        ),
        87 => 
        array (
          'name' => 'Store Locators',
          'flag' => 'ecommerce.settings.store-locators',
          'parent_flag' => 'ecommerce.settings',
        ),
        88 => 
        array (
          'name' => 'Invoice',
          'flag' => 'ecommerce.settings.invoices',
          'parent_flag' => 'ecommerce.settings',
        ),
        89 => 
        array (
          'name' => 'Product Review',
          'flag' => 'ecommerce.settings.product-reviews',
          'parent_flag' => 'ecommerce.settings',
        ),
        90 => 
        array (
          'name' => 'Customer',
          'flag' => 'ecommerce.settings.customers',
          'parent_flag' => 'ecommerce.settings',
        ),
        91 => 
        array (
          'name' => 'Shopping',
          'flag' => 'ecommerce.settings.shopping',
          'parent_flag' => 'ecommerce.settings',
        ),
        92 => 
        array (
          'name' => 'Tax',
          'flag' => 'ecommerce.settings.taxes',
          'parent_flag' => 'ecommerce.settings',
        ),
        93 => 
        array (
          'name' => 'Shipping',
          'flag' => 'ecommerce.settings.shipping',
          'parent_flag' => 'ecommerce.settings',
        ),
        94 => 
        array (
          'name' => 'Shipping Rules',
          'flag' => 'ecommerce.shipping-rule-items.index',
          'parent_flag' => 'ecommerce.settings',
        ),
        95 => 
        array (
          'name' => 'Create',
          'flag' => 'ecommerce.shipping-rule-items.create',
          'parent_flag' => 'ecommerce.shipping-rule-items.index',
        ),
        96 => 
        array (
          'name' => 'Edit',
          'flag' => 'ecommerce.shipping-rule-items.edit',
          'parent_flag' => 'ecommerce.shipping-rule-items.index',
        ),
        97 => 
        array (
          'name' => 'Delete',
          'flag' => 'ecommerce.shipping-rule-items.destroy',
          'parent_flag' => 'ecommerce.shipping-rule-items.index',
        ),
        98 => 
        array (
          'name' => 'Bulk Import',
          'flag' => 'ecommerce.shipping-rule-items.bulk-import',
          'parent_flag' => 'ecommerce.shipping-rule-items.index',
        ),
        99 => 
        array (
          'name' => 'Tracking',
          'flag' => 'ecommerce.settings.tracking',
          'parent_flag' => 'ecommerce.settings',
        ),
        100 => 
        array (
          'name' => 'Standard and Format',
          'flag' => 'ecommerce.settings.standard-and-format',
          'parent_flag' => 'ecommerce.settings',
        ),
        101 => 
        array (
          'name' => 'Checkout',
          'flag' => 'ecommerce.settings.checkout',
          'parent_flag' => 'ecommerce.settings',
        ),
        102 => 
        array (
          'name' => 'Return',
          'flag' => 'ecommerce.settings.return',
          'parent_flag' => 'ecommerce.settings',
        ),
        103 => 
        array (
          'name' => 'Flash Sale',
          'flag' => 'ecommerce.settings.flash-sale',
          'parent_flag' => 'ecommerce.settings',
        ),
        104 => 
        array (
          'name' => 'Product Specification',
          'flag' => 'ecommerce.settings.product-specification',
          'parent_flag' => 'ecommerce.settings',
        ),
        105 => 
        array (
          'name' => 'Export Product Categories',
          'flag' => 'product-categories.export',
          'parent_flag' => 'tools.data-synchronize',
        ),
        106 => 
        array (
          'name' => 'Import Product Categories',
          'flag' => 'product-categories.import',
          'parent_flag' => 'tools.data-synchronize',
        ),
        107 => 
        array (
          'name' => 'Export Orders',
          'flag' => 'orders.export',
          'parent_flag' => 'tools.data-synchronize',
        ),
        108 => 
        array (
          'name' => 'Product Specification',
          'flag' => 'ecommerce.product-specification.index',
          'parent_flag' => 'plugins.ecommerce',
        ),
        109 => 
        array (
          'name' => 'Specification Groups',
          'flag' => 'ecommerce.specification-groups.index',
          'parent_flag' => 'ecommerce.product-specification.index',
        ),
        110 => 
        array (
          'name' => 'Create',
          'flag' => 'ecommerce.specification-groups.create',
          'parent_flag' => 'ecommerce.specification-groups.index',
        ),
        111 => 
        array (
          'name' => 'Edit',
          'flag' => 'ecommerce.specification-groups.edit',
          'parent_flag' => 'ecommerce.specification-groups.index',
        ),
        112 => 
        array (
          'name' => 'Delete',
          'flag' => 'ecommerce.specification-groups.destroy',
          'parent_flag' => 'ecommerce.specification-groups.index',
        ),
        113 => 
        array (
          'name' => 'Specification Attributes',
          'flag' => 'ecommerce.specification-attributes.index',
          'parent_flag' => 'ecommerce.product-specification.index',
        ),
        114 => 
        array (
          'name' => 'Create',
          'flag' => 'ecommerce.specification-attributes.create',
          'parent_flag' => 'ecommerce.specification-attributes.index',
        ),
        115 => 
        array (
          'name' => 'Edit',
          'flag' => 'ecommerce.specification-attributes.edit',
          'parent_flag' => 'ecommerce.specification-attributes.index',
        ),
        116 => 
        array (
          'name' => 'Delete',
          'flag' => 'ecommerce.specification-attributes.destroy',
          'parent_flag' => 'ecommerce.specification-attributes.index',
        ),
        117 => 
        array (
          'name' => 'Specification Tables',
          'flag' => 'ecommerce.specification-tables.index',
          'parent_flag' => 'ecommerce.product-specification.index',
        ),
        118 => 
        array (
          'name' => 'Create',
          'flag' => 'ecommerce.specification-tables.create',
          'parent_flag' => 'ecommerce.specification-tables.index',
        ),
        119 => 
        array (
          'name' => 'Edit',
          'flag' => 'ecommerce.specification-tables.edit',
          'parent_flag' => 'ecommerce.specification-tables.index',
        ),
        120 => 
        array (
          'name' => 'Delete',
          'flag' => 'ecommerce.specification-tables.destroy',
          'parent_flag' => 'ecommerce.specification-tables.index',
        ),
      ),
      'general' => 
      array (
        'prefix' => 'ecommerce_',
        'display_big_money_in_million_billion' => false,
        'bulk-import' => 
        array (
          'mime_types' => 
          array (
            0 => 'application/vnd.ms-excel',
            1 => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            2 => 'text/csv',
            3 => 'application/csv',
            4 => 'text/plain',
          ),
          'mimes' => 
          array (
            0 => 'xls',
            1 => 'xlsx',
            2 => 'csv',
          ),
        ),
        'enable_faq_in_product_details' => true,
        'digital_products' => 
        array (
          'allowed_mime_types' => 
          array (
          ),
        ),
      ),
      'shipping' => 
      array (
        'settings' => 
        array (
          'prefix' => 'ecommerce_shipping_',
        ),
      ),
      'order' => 
      array (
        'default_order_start_number' => 10000000,
        'default_order_weight' => 0.01,
      ),
      'cart' => 
      array (
        'database' => 
        array (
          'connection' => 'mysql',
          'table' => 'ec_cart',
        ),
      ),
      'email' => 
      array (
        'name' => 'plugins/ecommerce::email.name',
        'description' => 'plugins/ecommerce::email.description',
        'templates' => 
        array (
          'welcome' => 
          array (
            'title' => 'plugins/ecommerce::email.welcome_title',
            'description' => 'plugins/ecommerce::email.welcome_description',
            'subject' => 'plugins/ecommerce::email.welcome_subject',
            'can_off' => true,
            'enabled' => false,
            'variables' => 
            array (
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
            ),
          ),
          'confirm-email' => 
          array (
            'title' => 'plugins/ecommerce::email.confirm_email_title',
            'description' => 'plugins/ecommerce::email.confirm_email_description',
            'subject' => 'plugins/ecommerce::email.confirm_email_subject',
            'can_off' => false,
            'variables' => 
            array (
              'verify_link' => 'plugins/ecommerce::email.verify_link',
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
            ),
          ),
          'password-reminder' => 
          array (
            'title' => 'plugins/ecommerce::email.password_reminder_title',
            'description' => 'plugins/ecommerce::email.password_reminder_description',
            'subject' => 'plugins/ecommerce::email.password_reminder_subject',
            'can_off' => false,
            'variables' => 
            array (
              'reset_link' => 'plugins/ecommerce::email.reset_link',
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
            ),
          ),
          'customer_new_order' => 
          array (
            'title' => 'plugins/ecommerce::email.customer_new_order_title',
            'description' => 'plugins/ecommerce::email.customer_new_order_description',
            'subject' => 'plugins/ecommerce::email.customer_new_order_subject',
            'can_off' => true,
            'enabled' => false,
            'variables' => 
            array (
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
              'customer_phone' => 'plugins/ecommerce::ecommerce.customer_phone',
              'customer_address' => 'plugins/ecommerce::ecommerce.customer_address',
              'shipping_method' => 'plugins/ecommerce::ecommerce.shipping_method',
              'payment_method' => 'plugins/ecommerce::ecommerce.payment_method',
              'product_list' => 'plugins/ecommerce::ecommerce.product_list',
              'order_note' => 'plugins/ecommerce::ecommerce.order_note',
              'order_id' => 'plugins/ecommerce::ecommerce.order_id',
            ),
          ),
          'customer_cancel_order' => 
          array (
            'title' => 'plugins/ecommerce::email.order_cancellation_title',
            'description' => 'plugins/ecommerce::email.customer_order_cancellation_description',
            'subject' => 'plugins/ecommerce::email.customer_order_cancellation_subject',
            'can_off' => true,
            'variables' => 
            array (
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
              'order_id' => 'plugins/ecommerce::ecommerce.order_id',
              'cancellation_reason' => 'plugins/ecommerce::order.order_cancellation_reason',
              'product_list' => 'plugins/ecommerce::ecommerce.product_list',
            ),
          ),
          'admin_cancel_order' => 
          array (
            'title' => 'plugins/ecommerce::email.admin_order_cancellation_title',
            'description' => 'plugins/ecommerce::email.admin_order_cancellation_description',
            'subject' => 'plugins/ecommerce::email.admin_order_cancellation_subject',
            'can_off' => true,
            'variables' => 
            array (
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
              'order_id' => 'plugins/ecommerce::ecommerce.order_id',
              'cancellation_reason' => 'plugins/ecommerce::order.order_cancellation_reason',
              'product_list' => 'plugins/ecommerce::ecommerce.product_list',
            ),
          ),
          'order_cancellation_to_admin' => 
          array (
            'title' => 'plugins/ecommerce::email.order_cancellation_to_admin_title',
            'description' => 'plugins/ecommerce::email.order_cancellation_to_admin_description',
            'subject' => 'plugins/ecommerce::email.order_cancellation_to_admin_subject',
            'can_off' => true,
            'enabled' => false,
            'variables' => 
            array (
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
              'order_id' => 'plugins/ecommerce::ecommerce.order_id',
              'cancellation_reason' => 'plugins/ecommerce::order.order_cancellation_reason',
              'product_list' => 'plugins/ecommerce::ecommerce.product_list',
            ),
          ),
          'customer_delivery_order' => 
          array (
            'title' => 'plugins/ecommerce::email.delivery_confirmation_title',
            'description' => 'plugins/ecommerce::email.delivery_confirmation_description',
            'subject' => 'plugins/ecommerce::email.delivery_confirmation_subject',
            'can_off' => true,
            'variables' => 
            array (
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
              'order_id' => 'plugins/ecommerce::ecommerce.order_id',
              'order_delivery_notes' => 'plugins/ecommerce::email.order_delivery_notes',
              'product_list' => 'plugins/ecommerce::ecommerce.product_list',
            ),
          ),
          'customer_order_delivered' => 
          array (
            'title' => 'plugins/ecommerce::email.order_delivered_title',
            'description' => 'plugins/ecommerce::email.order_delivered_description',
            'subject' => 'plugins/ecommerce::email.order_delivered_subject',
            'can_off' => true,
            'enabled' => false,
            'variables' => 
            array (
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
              'order_id' => 'plugins/ecommerce::ecommerce.order_id',
              'order_delivery_notes' => 'plugins/ecommerce::email.order_delivery_notes',
              'product_list' => 'plugins/ecommerce::ecommerce.product_list',
            ),
          ),
          'admin_new_order' => 
          array (
            'title' => 'plugins/ecommerce::email.admin_new_order_title',
            'description' => 'plugins/ecommerce::email.admin_new_order_description',
            'subject' => 'plugins/ecommerce::email.admin_new_order_subject',
            'can_off' => true,
            'enabled' => false,
            'variables' => 
            array (
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
              'customer_phone' => 'plugins/ecommerce::ecommerce.customer_phone',
              'customer_address' => 'plugins/ecommerce::ecommerce.customer_address',
              'shipping_method' => 'plugins/ecommerce::ecommerce.shipping_method',
              'payment_method' => 'plugins/ecommerce::ecommerce.payment_method',
              'product_list' => 'plugins/ecommerce::ecommerce.product_list',
              'order_note' => 'plugins/ecommerce::ecommerce.order_note',
              'order_id' => 'plugins/ecommerce::ecommerce.order_id',
            ),
          ),
          'order_confirm' => 
          array (
            'title' => 'plugins/ecommerce::email.order_confirmation_title',
            'description' => 'plugins/ecommerce::email.order_confirmation_description',
            'subject' => 'plugins/ecommerce::email.order_confirmation_subject',
            'can_off' => true,
            'variables' => 
            array (
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
              'customer_phone' => 'plugins/ecommerce::ecommerce.customer_phone',
              'customer_address' => 'plugins/ecommerce::ecommerce.customer_address',
              'shipping_method' => 'plugins/ecommerce::ecommerce.shipping_method',
              'payment_method' => 'plugins/ecommerce::ecommerce.payment_method',
              'product_list' => 'plugins/ecommerce::ecommerce.product_list',
              'order_note' => 'plugins/ecommerce::ecommerce.order_note',
              'order_id' => 'plugins/ecommerce::ecommerce.order_id',
            ),
          ),
          'order_confirm_payment' => 
          array (
            'title' => 'plugins/ecommerce::email.payment_confirmation_title',
            'description' => 'plugins/ecommerce::email.payment_confirmation_description',
            'subject' => 'plugins/ecommerce::email.payment_confirmation_subject',
            'can_off' => true,
            'variables' => 
            array (
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
              'customer_phone' => 'plugins/ecommerce::ecommerce.customer_phone',
              'customer_address' => 'plugins/ecommerce::ecommerce.customer_address',
              'shipping_method' => 'plugins/ecommerce::ecommerce.shipping_method',
              'payment_method' => 'plugins/ecommerce::ecommerce.payment_method',
              'product_list' => 'plugins/ecommerce::ecommerce.product_list',
              'order_note' => 'plugins/ecommerce::ecommerce.order_note',
              'order_id' => 'plugins/ecommerce::ecommerce.order_id',
            ),
          ),
          'order_recover' => 
          array (
            'title' => 'plugins/ecommerce::email.order_recover_title',
            'description' => 'plugins/ecommerce::email.order_recover_description',
            'subject' => 'plugins/ecommerce::email.order_recover_subject',
            'can_off' => true,
            'variables' => 
            array (
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
              'customer_phone' => 'plugins/ecommerce::ecommerce.customer_phone',
              'customer_address' => 'plugins/ecommerce::ecommerce.customer_address',
              'shipping_method' => 'plugins/ecommerce::ecommerce.shipping_method',
              'payment_method' => 'plugins/ecommerce::ecommerce.payment_method',
              'product_list' => 'plugins/ecommerce::ecommerce.product_list',
              'order_note' => 'plugins/ecommerce::ecommerce.order_note',
              'order_id' => 'plugins/ecommerce::ecommerce.order_id',
              'order_token' => 'plugins/ecommerce::ecommerce.order_token',
            ),
          ),
          'order-return-request' => 
          array (
            'title' => 'plugins/ecommerce::email.order_return_request_title',
            'description' => 'plugins/ecommerce::email.order_return_request_description',
            'subject' => 'plugins/ecommerce::email.order_return_request_subject',
            'can_off' => true,
            'variables' => 
            array (
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
              'customer_phone' => 'plugins/ecommerce::ecommerce.customer_phone',
              'customer_address' => 'plugins/ecommerce::ecommerce.customer_address',
              'list_order_products' => 'plugins/ecommerce::email.list_order_products',
              'order_id' => 'plugins/ecommerce::ecommerce.order_id',
              'order_note' => 'plugins/ecommerce::ecommerce.order_note',
              'return_reason' => 'plugins/ecommerce::order.order_return_reason',
              'product_list' => 'plugins/ecommerce::ecommerce.product_list',
            ),
          ),
          'invoice-payment-created' => 
          array (
            'title' => 'plugins/ecommerce::email.invoice_payment_created_title',
            'description' => 'plugins/ecommerce::email.invoice_payment_created_description',
            'subject' => 'plugins/ecommerce::email.invoice_payment_created_subject',
            'can_off' => true,
            'enabled' => false,
            'variables' => 
            array (
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
              'invoice_code' => 'plugins/ecommerce::email.invoice_code',
              'invoice_link' => 'plugins/ecommerce::email.invoice_link',
            ),
          ),
          'review_products' => 
          array (
            'title' => 'plugins/ecommerce::email.review_products_title',
            'description' => 'plugins/ecommerce::email.review_products_description',
            'subject' => 'plugins/ecommerce::email.review_products_subject',
            'can_off' => true,
            'enabled' => false,
            'variables' => 
            array (
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
              'product_review_list' => 'plugins/ecommerce::ecommerce.product_review_list',
            ),
          ),
          'download_digital_products' => 
          array (
            'title' => 'plugins/ecommerce::email.download_digital_products_title',
            'description' => 'plugins/ecommerce::email.download_digital_products_description',
            'subject' => 'plugins/ecommerce::email.download_digital_products_subject',
            'can_off' => false,
            'variables' => 
            array (
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
              'customer_phone' => 'plugins/ecommerce::ecommerce.customer_phone',
              'customer_address' => 'plugins/ecommerce::ecommerce.customer_address',
              'payment_method' => 'plugins/ecommerce::ecommerce.payment_method',
              'order_note' => 'plugins/ecommerce::ecommerce.order_note',
              'order_id' => 'plugins/ecommerce::ecommerce.order_id',
              'digital_product_list' => 'plugins/ecommerce::email.digital_product_list',
              'digital_products' => 'plugins/ecommerce::email.digital_products',
            ),
          ),
          'customer-deletion-request-confirmation' => 
          array (
            'title' => 'plugins/ecommerce::email.customer_deletion_request_confirmation_title',
            'description' => 'plugins/ecommerce::email.customer_deletion_request_confirmation_description',
            'subject' => 'plugins/ecommerce::email.customer_deletion_request_confirmation_subject',
            'can_off' => false,
            'variables' => 
            array (
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
              'customer_email' => 'plugins/ecommerce::ecommerce.customer_email',
              'confirm_url' => 'plugins/ecommerce::account-deletion.confirm_url',
            ),
          ),
          'customer-deletion-request-completed' => 
          array (
            'title' => 'plugins/ecommerce::email.customer_deletion_request_completed_title',
            'description' => 'plugins/ecommerce::email.customer_deletion_request_completed_description',
            'subject' => 'plugins/ecommerce::email.customer_deletion_request_completed_subject',
            'can_off' => false,
            'variables' => 
            array (
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
            ),
          ),
          'order-return-status-updated' => 
          array (
            'title' => 'plugins/ecommerce::email.order_return_status_updated_title',
            'description' => 'plugins/ecommerce::email.order_return_status_updated_description',
            'subject' => 'plugins/ecommerce::email.order_return_status_updated_subject',
            'can_off' => true,
            'variables' => 
            array (
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
              'order_id' => 'plugins/ecommerce::ecommerce.order_id',
              'description' => 'core/base::forms.description',
              'status' => 'core/base::forms.status',
              'product_list' => 'plugins/ecommerce::ecommerce.product_list',
            ),
          ),
          'payment-proof-upload-notification' => 
          array (
            'title' => 'plugins/ecommerce::email.payment_proof_upload_notification_title',
            'description' => 'plugins/ecommerce::email.payment_proof_upload_notification_description',
            'subject' => 'plugins/ecommerce::email.payment_proof_upload_notification_subject',
            'can_off' => true,
            'variables' => 
            array (
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
              'customer_email' => 'plugins/ecommerce::ecommerce.customer_email',
              'order_id' => 'plugins/ecommerce::ecommerce.order_id',
              'payment_link' => 'plugins/ecommerce::ecommerce.order_link',
              'order_link' => 'plugins/ecommerce::ecommerce.payment_link',
              'product_list' => 'plugins/ecommerce::ecommerce.product_list',
            ),
          ),
          'product-file-updated' => 
          array (
            'title' => 'plugins/ecommerce::email.product_file_updated_title',
            'description' => 'plugins/ecommerce::email.product_file_updated_description',
            'subject' => 'plugins/ecommerce::email.product_file_updated_subject',
            'can_off' => true,
            'enabled' => true,
            'variables' => 
            array (
              'customer_name' => 'plugins/ecommerce::ecommerce.customer_name',
              'product_name' => 'plugins/ecommerce::products.product_name',
              'product_link' => 'plugins/ecommerce::products.product_link',
              'download_link' => 'plugins/ecommerce::ecommerce.download_link',
              'update_time' => 'plugins/ecommerce::ecommerce.update_time',
              'product_files' => 'plugins/ecommerce::ecommerce.product_files',
            ),
          ),
        ),
      ),
    ),
    'faq' => 
    array (
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'FAQ',
          'flag' => 'plugin.faq',
        ),
        1 => 
        array (
          'name' => 'FAQ',
          'flag' => 'faq.index',
          'parent_flag' => 'plugin.faq',
        ),
        2 => 
        array (
          'name' => 'Create',
          'flag' => 'faq.create',
          'parent_flag' => 'faq.index',
        ),
        3 => 
        array (
          'name' => 'Edit',
          'flag' => 'faq.edit',
          'parent_flag' => 'faq.index',
        ),
        4 => 
        array (
          'name' => 'Delete',
          'flag' => 'faq.destroy',
          'parent_flag' => 'faq.index',
        ),
        5 => 
        array (
          'name' => 'FAQ Categories',
          'flag' => 'faq_category.index',
          'parent_flag' => 'plugin.faq',
        ),
        6 => 
        array (
          'name' => 'Create',
          'flag' => 'faq_category.create',
          'parent_flag' => 'faq_category.index',
        ),
        7 => 
        array (
          'name' => 'Edit',
          'flag' => 'faq_category.edit',
          'parent_flag' => 'faq_category.index',
        ),
        8 => 
        array (
          'name' => 'Delete',
          'flag' => 'faq_category.destroy',
          'parent_flag' => 'faq_category.index',
        ),
        9 => 
        array (
          'name' => 'FAQ',
          'flag' => 'faqs.settings',
          'parent_flag' => 'settings.others',
        ),
      ),
      'general' => 
      array (
        'schema_supported' => 
        array (
          0 => 'Botble\\Page\\Models\\Page',
          1 => 'Botble\\Blog\\Models\\Post',
        ),
      ),
    ),
    'gallery' => 
    array (
      'general' => 
      array (
        'supported' => 
        array (
          0 => 'Botble\\Gallery\\Models\\Gallery',
          1 => 'Botble\\Page\\Models\\Page',
          2 => 'Botble\\Blog\\Models\\Post',
        ),
        'enable_gallery_images_meta_box' => true,
      ),
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Galleries',
          'flag' => 'galleries.index',
          'parent_flag' => 'core.cms',
        ),
        1 => 
        array (
          'name' => 'Create',
          'flag' => 'galleries.create',
          'parent_flag' => 'galleries.index',
        ),
        2 => 
        array (
          'name' => 'Edit',
          'flag' => 'galleries.edit',
          'parent_flag' => 'galleries.index',
        ),
        3 => 
        array (
          'name' => 'Delete',
          'flag' => 'galleries.destroy',
          'parent_flag' => 'galleries.index',
        ),
      ),
    ),
    'location' => 
    array (
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Location',
          'flag' => 'plugin.location',
        ),
        1 => 
        array (
          'name' => 'Countries',
          'flag' => 'country.index',
          'parent_flag' => 'plugin.location',
        ),
        2 => 
        array (
          'name' => 'Create',
          'flag' => 'country.create',
          'parent_flag' => 'country.index',
        ),
        3 => 
        array (
          'name' => 'Edit',
          'flag' => 'country.edit',
          'parent_flag' => 'country.index',
        ),
        4 => 
        array (
          'name' => 'Delete',
          'flag' => 'country.destroy',
          'parent_flag' => 'country.index',
        ),
        5 => 
        array (
          'name' => 'States',
          'flag' => 'state.index',
          'parent_flag' => 'plugin.location',
        ),
        6 => 
        array (
          'name' => 'Create',
          'flag' => 'state.create',
          'parent_flag' => 'state.index',
        ),
        7 => 
        array (
          'name' => 'Edit',
          'flag' => 'state.edit',
          'parent_flag' => 'state.index',
        ),
        8 => 
        array (
          'name' => 'Delete',
          'flag' => 'state.destroy',
          'parent_flag' => 'state.index',
        ),
        9 => 
        array (
          'name' => 'Cities',
          'flag' => 'city.index',
          'parent_flag' => 'plugin.location',
        ),
        10 => 
        array (
          'name' => 'Create',
          'flag' => 'city.create',
          'parent_flag' => 'city.index',
        ),
        11 => 
        array (
          'name' => 'Edit',
          'flag' => 'city.edit',
          'parent_flag' => 'city.index',
        ),
        12 => 
        array (
          'name' => 'Delete',
          'flag' => 'city.destroy',
          'parent_flag' => 'city.index',
        ),
      ),
      'general' => 
      array (
        'supported' => 
        array (
        ),
      ),
    ),
    'newsletter' => 
    array (
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Newsletters',
          'flag' => 'newsletter.index',
        ),
        1 => 
        array (
          'name' => 'Delete',
          'flag' => 'newsletter.destroy',
          'parent_flag' => 'newsletter.index',
        ),
        2 => 
        array (
          'name' => 'Newsletters',
          'flag' => 'newsletter.settings',
          'parent_flag' => 'settings.others',
        ),
      ),
      'email' => 
      array (
        'name' => 'plugins/newsletter::newsletter.settings.email.templates.title',
        'description' => 'plugins/newsletter::newsletter.settings.email.templates.description',
        'templates' => 
        array (
          'subscriber_email' => 
          array (
            'title' => 'plugins/newsletter::newsletter.settings.email.templates.to_user.title',
            'description' => 'plugins/newsletter::newsletter.settings.email.templates.to_user.description',
            'subject' => 'plugins/newsletter::newsletter.settings.email.templates.to_user.subject',
            'can_off' => true,
            'variables' => 
            array (
              'newsletter_name' => 'plugins/newsletter::newsletter.settings.email.templates.to_user.newsletter_name',
              'newsletter_email' => 'plugins/newsletter::newsletter.settings.email.templates.to_user.newsletter_email',
              'newsletter_unsubscribe_link' => 'plugins/newsletter::newsletter.settings.email.templates.to_user.newsletter_unsubscribe_link',
              'newsletter_unsubscribe_url' => 'plugins/newsletter::newsletter.settings.email.templates.to_user.newsletter_unsubscribe_url',
            ),
          ),
          'admin_email' => 
          array (
            'title' => 'plugins/newsletter::newsletter.settings.email.templates.to_admin.title',
            'description' => 'plugins/newsletter::newsletter.settings.email.templates.to_admin.description',
            'subject' => 'plugins/newsletter::newsletter.settings.email.templates.to_admin.subject',
            'can_off' => true,
            'variables' => 
            array (
              'newsletter_email' => 'plugins/newsletter::newsletter.settings.email.templates.to_admin.newsletter_email',
            ),
          ),
        ),
        'variables' => 
        array (
        ),
      ),
    ),
    'payment' => 
    array (
      'payment' => 
      array (
        'currency' => 'USD',
      ),
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Payments',
          'flag' => 'payment.index',
        ),
        1 => 
        array (
          'name' => 'Settings',
          'flag' => 'payments.settings',
          'parent_flag' => 'payment.index',
        ),
        2 => 
        array (
          'name' => 'Delete',
          'flag' => 'payment.destroy',
          'parent_flag' => 'payment.index',
        ),
        3 => 
        array (
          'name' => 'Payment Logs',
          'flag' => 'payments.logs',
          'parent_flag' => 'payment.index',
        ),
        4 => 
        array (
          'name' => 'View',
          'flag' => 'payments.logs.show',
          'parent_flag' => 'payments.logs',
        ),
        5 => 
        array (
          'name' => 'Delete',
          'flag' => 'payments.logs.destroy',
          'parent_flag' => 'payments.logs',
        ),
      ),
    ),
    'sale-popup' => 
    array (
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Sale popups',
          'flag' => 'sale-popup.settings',
        ),
      ),
    ),
    'shippo' => 
    array (
      'general' => 
      array (
        'package_types' => 
        array (
          'parcel' => 'Parcel',
          'couriersplease_500g_satchel' => 'CouriersPlease 500g Satchel',
          'couriersplease_1kg_satchel' => 'CouriersPlease 1kg Satchel',
          'couriersplease_3kg_satchel' => 'CouriersPlease 3kg Satchel',
          'couriersplease_5kg_satchel' => 'CouriersPlease 5g Satchel',
          'DHLeC_Irregular' => 'DHL eCommerce Irregular',
          'DHLeC_SM_Flats' => 'DHL eCommerce Flats',
          'Fastway_Australia_Satchel_A2' => 'Fastway Australia Satchel A2',
          'Fastway_Australia_Satchel_A3' => 'Fastway Australia Satchel A3',
          'Fastway_Australia_Satchel_A4' => 'Fastway Australia Satchel A4',
          'Fastway_Australia_Satchel_A5' => 'Fastway Australia Satchel A5',
          'FedEx_Envelope' => 'FedEx Envelope',
          'FedEx_Padded_Pak' => 'FedEx Padded Pak',
          'FedEx_Pak_2' => 'FedEx Small Pak',
          'FedEx_Pak_1' => 'FedEx Large Pak',
          'FedEx_XL_Pak' => 'FedEx Extra Large Pak',
          'FedEx_Tube' => 'FedEx Tube',
          'FedEx_Box_10kg' => 'FedEx 10kg Box',
          'FedEx_Box_25kg' => 'FedEx 25kg Box',
          'FedEx_Box_Small_1' => 'FedEx Small Box (S1)',
          'FedEx_Box_Small_2' => 'FedEx Small Box (S2)',
          'FedEx_Box_Medium_1' => 'FedEx Medium Box (M1)',
          'FedEx_Box_Medium_2' => 'FedEx Medium Box (M2)',
          'FedEx_Box_Large_1' => 'FedEx Large Box (L1)',
          'FedEx_Box_Large_2' => 'FedEx Large Box (L2)',
          'FedEx_Box_Extra_Large_1' => 'FedEx Extra Large Box (X1)',
          'FedEx_Box_Extra_Large_2' => 'FedEx Extra Large Box (X2)',
          'UPS_Express_Envelope' => 'UPS Express Envelope',
          'UPS_Express_Legal_Envelope' => 'UPS Express Legal Envelope',
          'UPS_Express_Box' => 'UPS Express Box',
          'UPS_Express_Box_Small' => 'UPS Small Express Box',
          'UPS_Express_Box_Medium' => 'UPS Medium Express Box',
          'UPS_Express_Box_Large' => 'UPS Large Express Box',
          'UPS_Box_10kg' => 'UPS 10kg Box',
          'UPS_Box_25kg' => 'UPS 25kg Box',
          'UPS_Express_Tube' => 'UPS Express Tube',
          'UPS_Express_Pak' => 'UPS Express Pak',
          'UPS_Laboratory_Pak' => 'UPS Laboratory Pak',
          'UPS_Pad_Pak' => 'UPS Pad Pak',
          'UPS_Pallet' => 'UPS Pallet',
          'UPS_MI_BPM' => 'UPS BPM (Mail Innovations - Domestic & International)',
          'UPS_MI_BPM_Flat' => 'UPS BPM Flat (Mail Innovations - Domestic & International)',
          'UPS_MI_BPM_Parcel' => 'UPS BPM Parcel (Mail Innovations - Domestic & International)',
          'UPS_MI_First_Class' => 'UPS First Class (Mail Innovations - Domestic only)',
          'UPS_MI_Irregular' => 'UPS Irregular (Mail Innovations - Domestic only)',
          'UPS_MI_Machinable' => 'UPS Machinable (Mail Innovations - Domestic only)',
          'UPS_MI_MEDIA_MAIL' => 'UPS Media Mail (Mail Innovations - Domestic only)',
          'UPS_MI_Parcel_Post' => 'UPS Parcel Post (Mail Innovations - Domestic only)',
          'UPS_MI_Priority' => 'UPS Priority (Mail Innovations - Domestic only)',
          'UPS_MI_Standard_Flat' => 'UPS Standard Flat (Mail Innovations - Domestic only)',
          'UPS_MI_Flat' => 'UPS Flat (Mail Innovations - Domestic only)',
          'USPS_FlatRateCardboardEnvelope' => 'USPS Flat Rate Cardboard Envelope',
          'USPS_FlatRateEnvelope' => 'USPS Flat Rate Envelope',
          'USPS_FlatRateGiftCardEnvelope' => 'USPS Flat Rate Gift Card Envelope',
          'USPS_FlatRateLegalEnvelope' => 'USPS Flat Rate Legal Envelope',
          'USPS_FlatRatePaddedEnvelope' => 'USPS Flat Rate Padded Envelope',
          'USPS_FlatRateWindowEnvelope' => 'USPS Flat Rate Window Envelope',
          'USPS_IrregularParcel' => 'USPS Irregular Parcel',
          'USPS_LargeFlatRateBoardGameBox' => 'USPS Large Flat Rate Board Game Box',
          'USPS_LargeFlatRateBox' => 'USPS Large Flat Rate Box',
          'USPS_APOFlatRateBox' => 'USPS APO/FPO/DPO Large Flat Rate Box',
          'USPS_LargeVideoFlatRateBox' => 'USPS Flat Rate Large Video Box (Int\'l only)',
          'USPS_MediumFlatRateBox1' => 'USPS Medium Flat Rate Box 1',
          'USPS_MediumFlatRateBox2' => 'USPS Medium Flat Rate Box 2',
          'USPS_RegionalRateBoxA1' => 'USPS Regional Rate Box A1',
          'USPS_RegionalRateBoxA2' => 'USPS Regional Rate Box A2',
          'USPS_RegionalRateBoxB1' => 'USPS Regional Rate Box B1',
          'USPS_RegionalRateBoxB2' => 'USPS Regional Rate Box B2',
          'USPS_SmallFlatRateBox' => 'USPS Small Flat Rate Box',
          'USPS_SmallFlatRateEnvelope' => 'USPS Small Flat Rate Envelope',
          'USPS_SoftPack' => 'USPS Soft Pack Padded Envelope',
        ),
        'service_levels' => 
        array (
          'usps_priority' => 'USPS Priority Mail',
          'usps_priority_express' => 'USPS Priority Mail Express',
          'usps_first' => 'USPS First Class Mail/Package',
          'usps_parcel_select' => 'USPS Parcel Select',
          'usps_media_mail' => 'USPS Media Mail, only for existing Shippo customers with grandfathered Media Mail option.',
          'usps_priority_mail_international' => 'USPS Priority Mail International',
          'usps_priority_mail_express_international' => 'USPS Priority Mail Express International',
          'usps_first_class_package_international_service' => 'USPS First Class Package International',
          'fedex_ground' => 'FedEx Ground®',
          'fedex_home_delivery' => 'FedEx Home Delivery®',
          'fedex_smart_post' => 'FedEx SmartPost®',
          'fedex_2_day' => 'FedEx 2Day®',
          'fedex_2_day_am' => 'FedEx 2Day® A.M.',
          'fedex_express_saver' => 'FedEx Express Saver®',
          'fedex_standard_overnight' => 'FedEx Standard Overnight®',
          'fedex_priority_overnight' => 'FedEx Priority Overnight®',
          'fedex_first_overnight' => 'FedEx First Overnight®',
          'fedex_freight_priority' => 'FedEx Freight® Priority',
          'fedex_next_day_freight' => 'FedEx Next Day Freight',
          'fedex_freight_economy' => 'FedEx Freight® Economy',
          'fedex_first_freight' => 'FedEx First Freight',
          'fedex_international_economy' => 'FedEx International Economy®',
          'fedex_international_priority' => 'FedEx International Priority®',
          'fedex_international_first' => 'FedEx International First®',
          'fedex_europe_first_international_priority' => 'FedEx Europe International First®',
          'fedex_international_priority_express' => 'FedEx International Priority Express',
          'international_economy_freight' => 'FedEx International Economy® Freight',
          'international_priority_freight' => 'FedEx International Priority® Freight',
          'ups_standard' => 'UPS Standard℠',
          'ups_ground' => 'UPS Ground',
          'ups_saver' => 'UPS Saver®',
          'ups_3_day_select' => 'UPS 3 Day Select®',
          'ups_second_day_air' => 'UPS 2nd Day Air®',
          'ups_second_day_air_am' => 'UPS 2nd Day Air® A.M.',
          'ups_next_day_air' => 'UPS Next Day Air®',
          'ups_next_day_air_saver' => 'UPS Next Day Air Saver®',
          'ups_next_day_air_early_am' => 'UPS Next Day Air® Early',
          'ups_mail_innovations_domestic' => 'UPS Mail Innovations (domestic)',
          'ups_surepost' => 'UPS Surepost',
          'ups_surepost_bound_printed_matter' => 'UPS SurePost® Bound Printed Matter',
          'ups_surepost_lightweight' => 'UPS Surepost Lightweight',
          'ups_surepost_media' => 'UPS SurePost® Media',
          'ups_express' => 'UPS Express®',
          'ups_express_1200' => 'UPS Express 12:00',
          'ups_express_plus' => 'UPS Express Plus®',
          'ups_expedited' => 'UPS Expedited®',
          'apc_postal_parcelconnect_expedited' => 'APC parcelConnect Expedited',
          'apc_postal_parcelconnect_priority' => 'APC parcelConnect Priority',
          'apc_postal_parcelconnect_priority_delcon' => 'APC parcelConnect Priority Delcon',
          'apc_postal_parcelconnect_priority_pqw' => 'APC parcelConnect Priority PQW',
          'apc_postal_parcelconnect_book_service' => 'APC parcelConnect Book Service',
          'apc_postal_parcelconnect_standard' => 'APC parcelConnect Standard',
          'apc_postal_parcelconnect_epmi' => 'APC parcelConnect ePMI',
          'apc_postal_parcelconnect_epacket' => 'APC parcelConnect ePacket',
          'apc_postal_parcelconnect_epmei' => 'APC parcelConnect ePMEI',
          'asendia_us_priority_tracked' => 'Asendia USA Priority Tracked',
          'asendia_us_international_express' => 'Asendia USA International Express',
          'asendia_us_international_priority_airmail' => 'Asendia USA International Priority Airmail',
          'asendia_us_international_surface_airlift' => 'Asendia USA International Surface Air Lift',
          'asendia_us_priority_mail_international' => 'Asendia USA Priority Mail International',
          'asendia_us_priority_mail_express_international' => 'Asendia USA Priority Mail Express International',
          'asendia_us_epacket' => 'Asendia USA International ePacket',
          'asendia_us_other' => 'Asendia USA Other Services (custom)',
          'australia_post_express_post' => 'Australia Express Post',
          'australia_post_parcel_post' => 'Australia Parcel Post',
          'australia_post_pack_and_track_international' => 'Australia Pack and Track International',
          'australia_post_international_airmail' => 'Australia International Airmail',
          'australia_post_express_post_international' => 'Australia Express Post International',
          'australia_post_express_courier_international' => 'Australia Express Courier International',
          'australia_post_international_express' => 'Australia International Express',
          'australia_post_international_standard' => 'Australia International Standard',
          'australia_post_international_economy' => 'Australia International Economy',
          'axlehire_next_day' => 'AxleHire Next Day',
          'canada_post_regular_parcel' => 'Canada Post Regular Parcel',
          'canada_post_expedited_parcel' => 'Canada Post Expedited Parcel',
          'canada_post_priority' => 'Canada Post Priority',
          'canada_post_xpresspost' => 'Canada Post Xpresspost',
          'canada_post_xpresspost_international' => 'Canada Post Xpresspost International',
          'canada_post_xpresspost_usa' => 'Canada Post Xpresspost USA',
          'canada_post_expedited_parcel_usa' => 'Canada Post Expedited Parcel USA',
          'canada_post_tracked_packet_usa' => 'Canada Post Tracked Packet USA',
          'canada_post_small_packet_usa_air' => 'Canada Post Small Packet USA Air',
          'canada_post_tracked_packet_international' => 'Canada Post Tracked Packet International',
          'canada_post_small_packet_international_air' => 'Canada Post Small Package International Air',
          'cdl_next_day' => 'CDL Next Day',
          'couriersplease_domestic_priority_auth_to_leave' => 'CouriersPlease Domestic Priority - Authority To Leave/POPPoints',
          'couriersplease_domestic_priority_sign_required' => 'CouriersPlease Domestic Priority - Signature Required',
          'couriersplease_gold_domestic_auth_to_leave' => 'CouriersPlease Gold Domestic - Authority To Leave/POPPoints',
          'couriersplease_gold_domestic_sign_required' => 'CouriersPlease Gold Domestic - Signature Required',
          'couriersplease_off_peak_auth_to_leave' => 'CouriersPlease Off Peak - Authority To Leave/POPPoints',
          'couriersplease_off_peak_sign_required' => 'CouriersPlease Off Peak - Signature Required',
          'couriersplease_parcel_auth_to_leave' => 'CouriersPlease Parcel - Authority To Leave',
          'couriersplease_parcel_sign_required' => 'CouriersPlease Parcel - Signature Required',
          'couriersplease_road_express' => 'CouriersPlease Road Express',
          'couriersplease_satchel_auth_to_leave' => 'Satchel - Authority To Leave',
          'couriersplease_satchel_sign_required' => 'Satchel - Signature Required',
          'purolator_ground' => 'Purolator Ground',
          'purolator_ground9_am' => 'Purolator Ground 9am',
          'purolator_ground1030_am' => 'Purolator Ground 10:30am',
          'purolator_ground_distribution' => 'Purolator Ground Distribution',
          'purolator_ground_evening' => 'Purolator Ground Evening',
          'purolator_ground_us' => 'Purolator Ground US',
          'purolator_express' => 'Purolator Express',
          'purolator_express9_am' => 'Purolator Express 9am',
          'purolator_express1030_am' => 'Purolator Express 10am',
          'purolator_express_evening' => 'Purolator Express Evening',
          'purolator_express_us' => 'Purolator Express US',
          'purolator_express_us9_am' => 'Purolator Express US 9am',
          'purolator_express_us1030_am' => 'Purolator Express US 10:30am',
          'purolator_express_us1200' => 'Purolator Express US 12pm',
          'purolator_express_international' => 'Purolator Express International',
          'purolator_express_international9_am' => 'Purolator Express International 9am',
          'purolator_express_international1030_am' => 'Purolator Express International 10:30am',
          'purolator_express_international1200' => 'Purolator Express International 12pm',
          'dhl_express_domestic_express_doc' => 'DHL Domestic Express Doc',
          'dhl_express_economy_select_doc' => 'DHL Economy Select Doc',
          'dhl_express_worldwide_nondoc' => 'DHL Express Worldwide Nondoc',
          'dhl_express_worldwide_doc' => 'DHL Express Worldwide Doc',
          'dhl_express_worldwide' => 'DHL Worldwide',
          'dhl_express_worldwide_eu_doc' => 'DHL Express Worldwide EU Doc',
          'dhl_express_break_bulk_express_doc' => 'DHL Break Bulk Express Doc',
          'dhl_express_express_9_00_nondoc' => 'DHL Express 9:00 NonDoc',
          'dhl_express_economy_select_nondoc' => 'DHL Economy Select NonDoc',
          'dhl_express_break_bulk_economy_doc' => 'DHL Break Bulk Economy Doc',
          'dhl_express_express_9_00_doc' => 'DHL Express 9:00 Doc',
          'dhl_express_express_10_30_doc' => 'DHL Express 10:30 Doc',
          'dhl_express_express_10_30_nondoc' => 'DHL Express 10:30 NonDoc',
          'dhl_express_express_12_00_doc' => 'DHL Express 12:00 Doc',
          'dhl_express_europack_nondoc' => 'DHL Europack NonDoc',
          'dhl_express_express_envelope_doc' => 'DHL Express Envelope Doc',
          'dhl_express_express_12_00_nondoc' => 'DHL Express 12:00 NonDoc',
          'dhl_express_express_12_doc' => 'DHL Domestic Express 12:00',
          'dhl_express_worldwide_b2c_doc' => 'DHL Express Worldwide (B2C) Doc',
          'dhl_express_worldwide_b2c_nondoc' => 'DHL Express Worldwide (B2C) NonDoc',
          'dhl_express_medical_express' => 'DHL Medical Express',
          'dhl_express_express_easy_nondoc' => 'DHL Express Easy NonDoc',
          'dhl_ecommerce_marketing_parcel_expedited' => 'DHL eCommerce Marketing Parcel Expedited',
          'dhl_ecommerce_globalmail_business_ips' => 'DHL eCommerce Parcel International Expedited',
          'dhl_ecommerce_parcel_international_direct' => 'DHL eCommerce GlobalMail Business Standard',
          'dhl_ecommerce_parcels_expedited_max' => 'DHL eCommerce Parcels Expedited Max',
          'dhl_ecommerce_bpm_ground' => 'DHL eCommerce Bounded Printed Matter Ground',
          'dhl_ecommerce_priority_expedited' => 'DHL eCommerce Priority Expedited',
          'dhl_ecommerce_globalmail_packet_ipa' => 'DHL eCommerce GlobalMail Packet Priority',
          'dhl_ecommerce_globalmail_packet_isal' => 'DHL eCommerce GlobalMail Packet Standard',
          'dhl_ecommerce_easy_return_plus' => 'DHL eCommerce Easy Return Plus',
          'dhl_ecommerce_marketing_parcel_ground' => 'DHL eCommerce Marketing Parcel Ground',
          'dhl_ecommerce_first_class_parcel_expedited' => 'DHL eCommerce First Class Parcel Expedited',
          'dhl_ecommerce_globalmail_business_priority' => 'DHL eCommerce Parcel International Standard',
          'dhl_ecommerce_parcels_expedited' => 'DHL eCommerce Parcels Expedited',
          'dhl_ecommerce_globalmail_business_isal' => 'DHL eCommerce Parcel International Direct',
          'dhl_ecommerce_parcel_plus_expedited_max' => 'DHL eCommerce Parcel Plus Expedited Max',
          'dhl_ecommerce_globalmail_packet_plus' => 'DHL eCommerce GlobalMail Packet IPA',
          'dhl_ecommerce_parcels_ground' => 'DHL eCommerce Parcels Ground',
          'dhl_ecommerce_expedited' => 'DHL eCommerce Expedited',
          'dhl_ecommerce_parcel_plus_ground' => 'DHL eCommerce Parcel Plus Ground',
          'dhl_ecommerce_parcel_international_standard' => 'DHL eCommerce GlobalMail Business ISAL',
          'dhl_ecommerce_bpm_expedited' => 'DHL eCommerce Bounded Printed Matter Expedited',
          'dhl_ecommerce_parcel_international_expedited' => 'DHL eCommerce GlobalMail Business IPA',
          'dhl_ecommerce_globalmail_packet_priority' => 'DHL eCommerce GlobalMail Packet ISAL',
          'dhl_ecommerce_easy_return_light' => 'DHL eCommerce Easy Return Light',
          'dhl_ecommerce_parcel_plus_expedited' => 'DHL eCommerce Parcel Plus Expedited',
          'dhl_ecommerce_globalmail_business_standard' => 'DHL eCommerce GlobalMail Packet Plus',
          'dhl_ecommerce_ground' => 'DHL eCommerce Ground',
          'dhl_ecommerce_globalmail_packet_standard' => 'DHL eCommerce GlobalMail Business Priority',
          'dhl_germany_europaket' => 'DHL Germany Europaket',
          'dhl_germany_paket' => 'DHL Germany Paket',
          'dhl_germany_paket_connect' => 'DHL Germany Paket Connect',
          'dhl_germany_paket_international' => 'DHL Germany Paket International',
          'dhl_germany_paket_priority' => 'DHL Germany Paket Priority',
          'dhl_germany_paket_sameday' => 'DHL Germany Paket Sameday',
          'deutsche_post_postkarte' => 'Deutsche Post Postkarte',
          'deutsche_post_standardbrief' => 'Deutsche Post Standardbrief',
          'deutsche_post_kompaktbrief' => 'Deutsche Post Kompaktbrief',
          'deutsche_post_grossbrief' => 'Deutsche Post Grossbrief',
          'deutsche_post_maxibrief' => 'Deutsche Post Maxibrief',
          'deutsche_post_maxibrief_plus' => 'Deutsche Post Maxibrief Plus',
          'deutsche_post_warenpost_international_xs' => 'Deutsche Post Warenpost International XS',
          'deutsche_post_warenpost_international_s' => 'Deutsche Post Warenpost International S',
          'deutsche_post_warenpost_international_m' => 'Deutsche Post Warenpost International M',
          'deutsche_post_warenpost_international_l' => 'Deutsche Post Warenpost International L',
          'fastway_australia_parcel' => 'Fastway Australia Parcel',
          'fastway_australia_satchel' => 'Fastway Australia Satchel',
          'fastway_australia_box_small' => 'Fastway Australia Box Small',
          'fastway_australia_box_medium' => 'Fastway Australia Box Medium',
          'fastway_australia_box_large' => 'Fastway Australia Box Large',
          'globegistics_priority_mail_express_international' => 'Globegistics Priority Mail Express International',
          'globegistics_priority_mail_international' => 'Globegistics Priority Mail International',
          'globegistics_priority_mail_express_international_pds' => 'Globegistics Priority Mail Express International PreSort Drop Ship',
          'globegistics_priority_mail_international_pds' => 'Globegistics Priority Mail International PreSort Drop Ship',
          'globegistics_epacket' => 'Globegistics ePacket',
          'globegistics_ecom_tracked_ddp' => 'Globegistics eCom Tracked DDP',
          'globegistics_ecom_packet_ddp' => 'Globegistics eCom Packet DDP',
          'globegistics_ecom_priority_mail_international_ddp' => 'Globegistics eCom Priority Mail International DDP',
          'globegistics_ecom_priority_mail_express_international_ddp' => 'Globegistics eCom Priority Mail Express International DDP',
          'globegistics_ecom_extra' => 'Globegistics eCom Extra',
          'globegistics_ecom_international_priority_airmail' => 'Globegistics eCom International Priority Airmail',
          'globegistics_ecom_international_surface_airlift' => 'Globegistics eCom International Surface Air Lift',
          'gls_deutschland_business_parcel' => 'GLS Germany Business Parcel',
          'gls_france_business_parcel' => 'GLS France Business Parcel',
          'lso_ground' => 'LSO Ground',
          'lso_economy_next_day' => 'LSO Economy Next Day',
          'lso_saturday_delivery' => 'LSO Saturday Delivery',
          'lso_2nd_day' => 'LSO 2nd Day',
          'lso_priority_next_day' => 'LSO Priority Next Day',
          'lso_early_overnight' => 'LSO Early Overnight',
          'mondial_relay_pointrelais' => 'Mondial Relay Point Relais',
          'parcelforce_express48' => 'Parcelforce Express 48',
          'parcelforce_express24' => 'Parcelforce Express 24',
          'parcelforce_expressam' => 'Parcelforce Express AM',
          'rr_donnelley_domestic_economy_parcel' => 'RR Donnelley Domestic Economy Parcel',
          'rr_donnelley_domestic_priority_parcel' => 'RR Donnelley Domestic Priority Parcel ',
          'rr_donnelley_domestic_parcel_bpm' => 'RR Donnelley Domestic Parcel BPM',
          'rr_donnelley_priority_domestic_priority_parcel_bpm' => 'RR Donnelley Domestic Priority Parcel BPM',
          'rr_donnelley_priority_parcel_delcon' => 'RR Donnelley International Priority Parcel DelCon',
          'rr_donnelley_priority_parcel_nondelcon' => 'RR Donnelley International Priority Parcel NonDelcon',
          'rr_donnelley_economy_parcel' => 'RR Donnelley Economy Parcel Service ',
          'rr_donnelley_ipa' => 'RR Donnelley International Priority Airmail (IPA)',
          'rr_donnelley_courier' => 'RR Donnelley International Courier',
          'rr_donnelley_isal' => 'RR Donnelley International Surface Air Lift (ISAL)',
          'rr_donnelley_epacket' => 'RR Donnelley e-Packet',
          'rr_donnelley_pmi' => 'RR Donnelley Priority Mail International',
          'rr_donnelley_emi' => 'RR Donnelley Express Mail International',
          'sendle_parcel' => 'Sendle Parcel',
          'newgistics_parcel_select_lightweight' => 'Newgistics Parcel Select Lightweight',
          'newgistics_parcel_select' => 'Newgistics Parcel Select',
          'newgistics_priority_mail' => 'Newgistics Priority Mail',
          'newgistics_first_class_mail' => 'Newgistics First Class Mail',
          'ontrac_ground' => 'OnTrac Ground',
          'ontrac_sunrise_gold' => 'OnTrac Sunrise Gold',
          'ontrac_sunrise' => 'OnTrac Sunrise',
          'lasership_routed_delivery' => 'Lasership Routed Delivery',
          'hermes_uk_courier_collection' => 'Hermes UK Courier Collection',
          'hermes_uk_parcelshop_dropoff' => 'Hermes UK ParcelShop Drop-Off',
          'FedEx_Box_10kg' => 'FedEx® 10kg Box',
          'FedEx_Box_25kg' => 'FedEx® 25kg Box',
          'FedEx_Box_Extra_Large_1' => 'FedEx® Extra Large Box (X1)',
          'FedEx_Box_Extra_Large_2' => 'FedEx® Extra Large Box (X2)',
          'FedEx_Box_Large_1' => 'FedEx® Large Box (L1)',
          'FedEx_Box_Large_2' => 'FedEx® Large Box (L2)',
          'FedEx_Box_Medium_1' => 'FedEx® Medium Box (M1)',
          'FedEx_Box_Medium_2' => 'FedEx® Medium Box (M2)',
          'FedEx_Box_Small_1' => 'FedEx® Small Box (S1)',
          'FedEx_Box_Small_2' => 'FedEx® Small Box (S2)',
          'FedEx_Envelope' => 'FedEx® Envelope',
          'FedEx_Padded_Pak' => 'FedEx® Padded Pak',
          'FedEx_Pak_1' => 'FedEx® Large Pak',
          'FedEx_Pak_2' => 'FedEx® Small Pak',
          'FedEx_Tube' => 'FedEx® Tube',
          'FedEx_XL_Pak' => 'FedEx® Extra Large Pak',
          'UPS_Box_10kg' => 'Box 10kg',
          'UPS_Box_25kg' => 'Box 25kg',
          'UPS_Express_Box' => 'UPS Express Box',
          'UPS_Express_Box_Large' => 'UPS Express Box Large',
          'UPS_Express_Box_Medium' => 'UPS Express Box Medium',
          'UPS_Express_Box_Small' => 'UPS Express Box Small',
          'UPS_Express_Envelope' => 'UPS Express Envelope',
          'UPS_Express_Hard_Pak' => 'UPS Express Hard Pak',
          'UPS_Express_Legal_Envelope' => 'UPS Express Legal Envelope',
          'UPS_Express_Pak' => 'UPS Express Pak',
          'UPS_Express_Tube' => 'UPS Express Tube',
          'UPS_Laboratory_Pak' => 'Laboratory Pak',
          'UPS_MI_BPM' => 'BPM (Mail Innovations - Domestic &amp; International)',
          'UPS_MI_BPM_Flat' => 'BPM Flat (Mail Innovations - Domestic &amp; International)',
          'UPS_MI_BPM_Parcel' => 'BPM Parcel (Mail Innovations - Domestic &amp; International)',
          'UPS_MI_First_Class' => 'First Class (Mail Innovations - Domestic only)',
          'UPS_MI_Flat' => 'Flat (Mail Innovations - Domestic only)',
          'UPS_MI_Irregular' => 'Irregular (Mail Innovations - Domestic only)',
          'UPS_MI_Machinable' => 'Machinable (Mail Innovations - Domestic only)',
          'UPS_MI_MEDIA_MAIL' => 'Media Mail (Mail Innovations - Domestic only)',
          'UPS_MI_Parcel_Post' => 'Parcel Post (Mail Innovations - Domestic only)',
          'UPS_MI_Priority' => 'Priority (Mail Innovations - Domestic only)',
          'UPS_MI_Standard_Flat' => 'Standard Flat (Mail Innovations - Domestic only)',
          'UPS_Pad_Pak' => 'UPS Pad Pak',
          'UPS_Pallet' => 'UPS Pallet',
          'USPS_FlatRateCardboardEnvelope' => 'USPS Flat Rate Cardboard Envelope',
          'USPS_FlatRateEnvelope' => 'USPS Flat Rate Envelope',
          'USPS_FlatRateGiftCardEnvelope' => 'USPS Flat Rate Gift Card Envelope',
          'USPS_FlatRateLegalEnvelope' => 'USPS Flat Rate Legal Envelope',
          'USPS_FlatRatePaddedEnvelope' => 'USPS Flat Rate Padded Envelope',
          'USPS_FlatRateWindowEnvelope' => 'USPS Flat Rate Window Envelope',
          'USPS_IrregularParcel' => 'USPS Irregular Parcel',
          'USPS_LargeFlatRateBoardGameBox' => 'USPS Large Flat Rate Board Game Box',
          'USPS_LargeFlatRateBox' => 'USPS Large Flat Rate Box',
          'USPS_APOFlatRateBox' => 'USPS APO/FPO/DPO Large Flat Rate Box',
          'USPS_LargeVideoFlatRateBox' => 'USPS Flat Rate Large Video Box (Int\'l only)',
          'USPS_MediumFlatRateBox1' => 'USPS Medium Flat Rate Box 1',
          'USPS_MediumFlatRateBox2' => 'Medium Flat Rate Box 2',
          'USPS_RegionalRateBoxA1' => 'USPS Regional Rate Box A1',
          'USPS_RegionalRateBoxA2' => 'USPS Regional Rate Box A2',
          'USPS_RegionalRateBoxB1' => 'USPS Regional Rate Box B1',
          'USPS_RegionalRateBoxB2' => 'USPS Regional Rate Box B2',
          'USPS_SmallFlatRateBox' => 'USPS Small Flat Rate Box',
          'USPS_SmallFlatRateEnvelope' => 'USPS Small Flat Rate Envelope',
          'USPS_SoftPack' => 'USPS Soft Pack Padded Envelope',
        ),
      ),
    ),
    'simple-slider' => 
    array (
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Simple Sliders',
          'flag' => 'simple-slider.index',
        ),
        1 => 
        array (
          'name' => 'Create',
          'flag' => 'simple-slider.create',
          'parent_flag' => 'simple-slider.index',
        ),
        2 => 
        array (
          'name' => 'Edit',
          'flag' => 'simple-slider.edit',
          'parent_flag' => 'simple-slider.index',
        ),
        3 => 
        array (
          'name' => 'Delete',
          'flag' => 'simple-slider.destroy',
          'parent_flag' => 'simple-slider.index',
        ),
        4 => 
        array (
          'name' => 'Slider Items',
          'flag' => 'simple-slider-item.index',
          'parent_flag' => 'simple-slider.index',
        ),
        5 => 
        array (
          'name' => 'Create',
          'flag' => 'simple-slider-item.create',
          'parent_flag' => 'simple-slider-item.index',
        ),
        6 => 
        array (
          'name' => 'Edit',
          'flag' => 'simple-slider-item.edit',
          'parent_flag' => 'simple-slider-item.index',
        ),
        7 => 
        array (
          'name' => 'Delete',
          'flag' => 'simple-slider-item.destroy',
          'parent_flag' => 'simple-slider-item.index',
        ),
        8 => 
        array (
          'name' => 'Simple Slider Settings',
          'flag' => 'simple-slider.settings',
          'parent_flag' => 'simple-slider-item.index',
        ),
      ),
    ),
    'social-login' => 
    array (
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Social Login',
          'flag' => 'social-login.settings',
          'parent_flag' => 'settings.others',
        ),
      ),
      'general' => 
      array (
        'supported' => 
        array (
          'customer' => 
          array (
            'guard' => 'customer',
            'model' => 'Botble\\Ecommerce\\Models\\Customer',
            'login_url' => 'http://localhost/main/login',
            'redirect_url' => 'http://localhost/main',
          ),
        ),
      ),
    ),
    'sslcommerz' => 
    array (
      'sslcommerz' => 
      array (
        'apiUrl' => 
        array (
          'make_payment' => '/gwprocess/v4/api.php',
          'transaction_status' => '/validator/api/merchantTransIDvalidationAPI.php',
          'order_validate' => '/validator/api/validationserverAPI.php',
          'refund_payment' => '/validator/api/merchantTransIDvalidationAPI.php',
          'refund_status' => '/validator/api/merchantTransIDvalidationAPI.php',
          'payment_detail' => '/validator/api/merchantTransIDvalidationAPI.php',
        ),
        'connect_from_localhost' => true,
        'success_url' => '/sslcommerz/payment/success',
        'failed_url' => '/sslcommerz/payment/fail',
        'cancel_url' => '/sslcommerz/payment/cancel',
        'ipn_url' => '/sslcommerz/payment/ipn',
      ),
    ),
    'team' => 
    array (
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Teams',
          'flag' => 'team.index',
        ),
        1 => 
        array (
          'name' => 'Create',
          'flag' => 'team.create',
          'parent_flag' => 'team.index',
        ),
        2 => 
        array (
          'name' => 'Edit',
          'flag' => 'team.edit',
          'parent_flag' => 'team.index',
        ),
        3 => 
        array (
          'name' => 'Delete',
          'flag' => 'team.destroy',
          'parent_flag' => 'team.index',
        ),
      ),
    ),
    'testimonial' => 
    array (
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Testimonial',
          'flag' => 'testimonial.index',
        ),
        1 => 
        array (
          'name' => 'Create',
          'flag' => 'testimonial.create',
          'parent_flag' => 'testimonial.index',
        ),
        2 => 
        array (
          'name' => 'Edit',
          'flag' => 'testimonial.edit',
          'parent_flag' => 'testimonial.index',
        ),
        3 => 
        array (
          'name' => 'Delete',
          'flag' => 'testimonial.destroy',
          'parent_flag' => 'testimonial.index',
        ),
      ),
    ),
    'translation' => 
    array (
      'general' => 
      array (
        'exclude_groups' => 
        array (
        ),
      ),
      'permissions' => 
      array (
        0 => 
        array (
          'name' => 'Localization',
          'flag' => 'plugins.translation',
          'parent_flag' => 'settings.index',
        ),
        1 => 
        array (
          'name' => 'Locales',
          'flag' => 'translations.locales',
          'parent_flag' => 'plugins.translation',
        ),
        2 => 
        array (
          'name' => 'Theme translations',
          'flag' => 'translations.theme-translations',
          'parent_flag' => 'plugins.translation',
        ),
        3 => 
        array (
          'name' => 'Other translations',
          'flag' => 'translations.index',
          'parent_flag' => 'plugins.translation',
        ),
        4 => 
        array (
          'name' => 'Export Theme translations',
          'flag' => 'theme-translations.export',
          'parent_flag' => 'tools.data-synchronize',
        ),
        5 => 
        array (
          'name' => 'Export Other Translations',
          'flag' => 'other-translations.export',
          'parent_flag' => 'tools.data-synchronize',
        ),
        6 => 
        array (
          'name' => 'Import Theme Translations',
          'flag' => 'theme-translations.import',
          'parent_flag' => 'tools.data-synchronize',
        ),
        7 => 
        array (
          'name' => 'Import Other Translations',
          'flag' => 'other-translations.import',
          'parent_flag' => 'tools.data-synchronize',
        ),
      ),
    ),
  ),
  'paystack' => 
  array (
    'publicKey' => NULL,
    'secretKey' => NULL,
    'paymentUrl' => 'https://api.paystack.co',
  ),
  'tinker' => 
  array (
    'commands' => 
    array (
    ),
    'alias' => 
    array (
    ),
    'dont_alias' => 
    array (
      0 => 'App\\Nova',
    ),
  ),
);
