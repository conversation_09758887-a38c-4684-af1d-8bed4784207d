<ul <?php echo $options; ?>>
    <?php $__currentLoopData = $menu_nodes->loadMissing('metadata'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $node): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li class="<?php echo \Illuminate\Support\Arr::toCssClasses([$node->css_class => $node->css_class]); ?>">
            <a
                href="<?php echo e(url($node->url)); ?>"
                title="<?php echo e($node->title); ?>"
                <?php if($node->target !== '_self'): ?> target="<?php echo e($node->target); ?>" <?php endif; ?>
            >
                <?php echo $node->icon_html; ?>


                <?php echo e($node->title); ?>

            </a>
        </li>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</ul>
<?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/partials/footer-menu.blade.php ENDPATH**/ ?>