<?php
    $groupedCategories = $categories->groupBy('parent_id');

    $currentCategories = $groupedCategories->get(0);
?>

<?php if($currentCategories): ?>
    <?php $__currentLoopData = $currentCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php
            $hasChildren = $groupedCategories->has($category->id);
        ?>
        <li class="<?php echo \Illuminate\Support\Arr::toCssClasses(['has-dropdown' => $hasChildren]); ?>">
            <a href="<?php echo e(route('public.single', $category->url)); ?>">
                <?php if($categoryImage = $category->icon_image): ?>
                    <img src="<?php echo e(RvMedia::getImageUrl($categoryImage)); ?>" alt="<?php echo e($category->name); ?>" width="18" height="18" class="me-1">
                <?php elseif($categoryIcon = $category->icon): ?>
                    <i class="<?php echo e($categoryIcon); ?>"></i>
                <?php endif; ?>
                <?php echo e($category->name); ?>

            </a>
            <?php if($hasChildren && $currentCategories = $groupedCategories->get($category->id)): ?>
                <ul class="submenu">
                    <?php $__currentLoopData = $currentCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $childCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li><a href="<?php echo e(route('public.single', $childCategory->url )); ?>"><?php echo e($childCategory->name); ?></a></li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            <?php endif; ?>
        </li>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/partials/categories-dropdown-mobile.blade.php ENDPATH**/ ?>