<?php if(theme_option('sticky_header_enabled', 'yes') == 'yes'): ?>
    <div id="header-sticky" class="logo-area tp-sticky-one mainmenu-5">
        <?php echo Theme::partial('header-middle'); ?>

    </div>
<?php endif; ?>

<div id="header-tab-sticky" class="tp-md-lg-header d-none d-md-block d-xl-none pt-30 pb-30">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-3 col-md-4 d-flex align-items-center">
                <div class="flex-auto header-canvas">
                    <button class="tp-menu-toggle" title="open">
                        <i class="far fa-bars"></i>
                    </button>
                </div>
                <?php echo Theme::partial('logo'); ?>

            </div>
            <?php if(is_plugin_active('ecommerce')): ?>
                <div class="col-lg-9 col-md-8">
                    <div class="header-meta-info d-flex align-items-center justify-content-between">
                        <?php echo Theme::get('headerSearchBar'); ?>

                        <div class="header-meta__social d-flex align-items-center ml-25">
                            <?php if(EcommerceHelper::isCartEnabled()): ?>
                                <button class="header-cart p-relative tp-cart-toggle" title="search">
                                    <i class="fal fa-shopping-cart"></i>
                                    <span class="tp-product-count"><?php echo e(Cart::instance('cart')->count()); ?></span>
                                </button>
                            <?php endif; ?>

                            <?php if(EcommerceHelper::isCompareEnabled()): ?>
                                <a href="<?php echo e(route('public.compare')); ?>" class="header-cart p-relative">
                                    <i class="fal fa-exchange"></i>
                                    <span class="tp-product-compare-count"><?php echo e(Cart::instance('compare')->count()); ?></span>
                                </a>
                            <?php endif; ?>

                            <?php if(auth()->guard('customer')->check()): ?>
                                <a href="<?php echo e(route('customer.overview')); ?>" title="<?php echo e(auth('customer')->user()->name); ?>"><i class="fal fa-user"></i></a>
                            <?php else: ?>
                                <a href="<?php echo e(route('customer.login')); ?>" title="<?php echo e(__('Login')); ?>"><i class="fal fa-user"></i></a>
                            <?php endif; ?>

                            <?php if(EcommerceHelper::isWishlistEnabled()): ?>
                                <a href="<?php echo e(route('public.wishlist')); ?>"><i class="fal fa-heart"></i></a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<div <?php if(theme_option('sticky_header_mobile_enabled', 'yes') == 'yes'): ?> id="header-mob-sticky" <?php endif; ?> class="<?php echo \Illuminate\Support\Arr::toCssClasses(['tp-md-lg-header d-md-none pt-20 pb-20', $headerMobileStickyClass ?? null]); ?>">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-3 d-flex align-items-center">
                <div class="flex-auto header-canvas">
                    <button class="tp-menu-toggle" title="open">
                        <i class="far fa-bars"></i>
                    </button>
                </div>
            </div>
            <div class="col-6">
                <div class="text-center">
                    <?php echo Theme::partial('logo'); ?>

                </div>
            </div>
            <?php if(is_plugin_active('ecommerce')): ?>
                <div class="col-3">
                    <div class="header-meta-info d-flex align-items-center justify-content-end ml-25">
                        <button class="header-cart p-relative tp-search-sidebar-toggle" title="search">
                            <i class="fal fa-search"></i>
                        </button>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<div class="tpsideinfo">
    <button class="tpsideinfo__close">
        <?php echo e(__('Close')); ?>

        <i class="ml-10 fal fa-times"></i>
    </button>

    <div class="tpsideinfo__nabtab mb-4 mt-30">
        <?php echo Theme::partial('mobile.menu-tab-content'); ?>

    </div>

    <?php if(is_plugin_active('ecommerce')): ?>
        <?php if(EcommerceHelper::isCompareEnabled()): ?>
            <div class="tpsideinfo__wishlist-link">
                <a href="<?php echo e(route('public.compare')); ?>" class="header-cart d-block p-relative">
                    <i class="fal fa-exchange"></i> <?php echo e(__('Compare Products')); ?>

                    <span class="tp-product-compare-count"><?php echo e(Cart::instance('compare')->count()); ?></span>
                </a>
            </div>
        <?php endif; ?>

        <?php if(EcommerceHelper::isOrderTrackingEnabled()): ?>
            <div class="tpsideinfo__wishlist-link">
                <a href="<?php echo e(route('public.orders.tracking')); ?>">
                    <i class="fal fa-truck"></i> <?php echo e(__('Order Tracking')); ?>

                </a>
            </div>
        <?php endif; ?>

        <div class="tpsideinfo__account-link">
            <?php if(auth()->guard('customer')->check()): ?>
                <a href="<?php echo e(route('customer.overview')); ?>" title="<?php echo e(auth('customer')->user()->name); ?>"><i class="fal fa-user"></i> <?php echo e(auth('customer')->user()->name); ?></a>
            <?php else: ?>
                <a href="<?php echo e(route('customer.login')); ?>" title="<?php echo e(__('Login')); ?>"><i class="fal fa-user"></i> <?php echo e(__('Login / Register')); ?></a>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <div class="tpsideinfo__switcher navbar-collapse collapse show mb-4" id="navbarSupportedContent" style="">
        <ul class="mb-2 navbar-nav me-auto mb-lg-0">
            <?php echo Theme::partial('language-switcher', ['mobile' => true]); ?>

            <?php echo Theme::partial('currency-switcher', ['mobile' => true]); ?>

        </ul>
    </div>
</div>

<div class="body-overlay"></div>

<div class="tpcartinfo tp-cart-info-area p-relative">
    <button class="tpcart__close" title="close">
        <i class="fal fa-times"></i>
    </button>
    <div class="tpcart">
        <h4 class="tpcart__title"><?php echo e(__('Your Cart')); ?></h4>
        <div class="tpcart__product">
            <?php echo $__env->make(Theme::getThemeNamespace('views.ecommerce.includes.mini-cart'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
        <?php if($cartFooterDescription = theme_option('cart_footer_description')): ?>
            <div class="text-center tpcart__free-shipping">
                <span><?php echo BaseHelper::clean($cartFooterDescription); ?></span>
            </div>
        <?php endif; ?>
    </div>
</div>

<div class="tpsideinfo tpsidecategories">
    <button class="tpsideinfo__close">
        <?php echo e(__('Close')); ?>

        <i class="ml-10 fal fa-times"></i>
    </button>

    <div class="tpsideinfo__nabtab mt-30 mb-4">
        <?php echo Theme::partial('mobile.categories-tab-content', compact('categories')); ?>

    </div>
</div>

<div class="tpsideinfo tpsidesearch">
    <button class="tpsideinfo__close">
        <?php echo e(__('Close')); ?>

        <i class="ml-10 fal fa-times"></i>
    </button>

    <?php if(is_plugin_active('ecommerce')): ?>
        <div class="text-center tpsideinfo__search pt-35">
            <span class="mb-20 tpsideinfo__search-title"><?php echo e(__('What Are You Looking For?')); ?></span>
            <form action="<?php echo e(route('public.products')); ?>" class="position-relative form--quick-search" data-url="<?php echo e(route('public.ajax.search-products')); ?>" method="GET">
                <input type="text" name="q" class="input-search-product" placeholder="<?php echo e(__('Search Products...')); ?>" value="<?php echo e(BaseHelper::stringify(request()->query('q'))); ?>" autocomplete="off">
                <button title="search"><i class="fal fa-search"></i></button>
                <div class="panel--search-result"></div>
            </form>
        </div>
    <?php endif; ?>
</div>

<div class="cartbody-overlay"></div>
<?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/partials/navbar.blade.php ENDPATH**/ ?>